package com.sky.handler;

import com.sky.exception.BaseException;
import com.sky.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLIntegrityConstraintViolationException;

/**
 * 全局异常处理器，处理项目中抛出的业务异常
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * 捕获业务异常
     * 处理项目中自定义的业务异常，如账号不存在、密码错误、账号被锁定等
     * 将异常信息包装成统一的响应格式返回给前端
     *
     * @param ex 业务异常对象，继承自BaseException
     * @return Result 统一响应结果，包含错误信息
     */
    @ExceptionHandler
    public Result exceptionHandler(BaseException ex){
        log.error("异常信息：{}", ex.getMessage());
        return Result.error(ex.getMessage());
    }

    /**
     * 捕获SQL完整性约束违反异常
     * 主要处理数据库唯一约束冲突，如用户名重复等情况
     * 解析异常信息并返回用户友好的错误提示
     *
     * @param ex SQL完整性约束违反异常
     * @return Result 统一响应结果，包含友好的错误信息
     */
    @ExceptionHandler
    public Result exceptionHandler(SQLIntegrityConstraintViolationException ex){
        //Duplicate entry '蓬治文' for key 'employee.idx_username
        String message = ex.getMessage();
        if(message.contains("Duplicate entry")){
            String[] split = message.split(" ");
            String msg = split[2] + "已存在";
            log.error("异常信息：{}", msg);
            return Result.error(msg);
        }else {
            return Result.error("未知错误");
        }
    }

    /**
     * 捕获运行时异常
     * 处理系统运行时发生的各种异常，如空指针异常、类型转换异常等
     * 目前已注释，便于开发阶段查看详细错误信息进行调试
     *
     * @param ex 运行时异常对象
     * @return Result 统一响应结果，包含错误信息
     */
    // 临时注释掉，方便查看详细错误信息
    /*
    @ExceptionHandler
    public Result exceptionHandler(RuntimeException ex){
        log.error("运行时异常：{}", ex.getMessage());
        return Result.error(ex.getMessage());
    }
    */

}
