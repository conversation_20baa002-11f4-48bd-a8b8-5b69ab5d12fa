package com.sky.task;

import com.alibaba.fastjson.JSON;
import com.sky.entity.Orders;
import com.sky.mapper.OrderMapper;
import com.sky.websocket.WebSocketServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 定时任务类，定时处理订单
 */
@Component
@Slf4j
public class MyTask {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private WebSocketServer webSocketServer;

    /**
     * 处理超时订单的方法
     * 每分钟触发一次，处理下单后超过15分钟仍未支付的订单
     */
    @Scheduled(cron = "0 * * * * ?") // 每分钟执行一次
    public void processTimeoutOrder() {
        log.info("定时处理超时订单：{}", LocalDateTime.now());

        LocalDateTime time = LocalDateTime.now().plusMinutes(-15);

        // 查询超时未支付的订单（状态为1：待付款，且下单时间超过15分钟）
        List<Orders> ordersList = orderMapper.getByStatusAndOrderTimeLT(Orders.PENDING_PAYMENT, time);

        if (ordersList != null && ordersList.size() > 0) {
            for (Orders orders : ordersList) {
                log.info("处理超时订单：{}", orders.getNumber());
                orders.setStatus(Orders.CANCELLED);
                orders.setCancelReason("订单超时，自动取消");
                orders.setCancelTime(LocalDateTime.now());
                orderMapper.update(orders);
            }
        }
    }

    /**
     * 处理一直处于派送中状态的订单
     * 每天凌晨1点触发一次，处理上一个工作日处于派送中状态的订单
     */
    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1:00执行
    public void processDeliveryOrder() {
        log.info("定时处理处于派送中的订单：{}", LocalDateTime.now());

        LocalDateTime time = LocalDateTime.now().plusMinutes(-60);

        // 查询派送中且超过1小时的订单（状态为4：派送中）
        List<Orders> ordersList = orderMapper.getByStatusAndOrderTimeLT(Orders.DELIVERY_IN_PROGRESS, time);

        if (ordersList != null && ordersList.size() > 0) {
            for (Orders orders : ordersList) {
                log.info("自动完成派送订单：{}", orders.getNumber());
                orders.setStatus(Orders.COMPLETED);
                orders.setDeliveryTime(LocalDateTime.now());
                orderMapper.update(orders);
            }
        }
    }

    /**
     * 处理长时间未接单的订单提醒
     * 每5分钟触发一次，检查待接单超过10分钟的订单
     */
    @Scheduled(cron = "0 */5 * * * ?") // 每5分钟执行一次
    public void processUnconfirmedOrder() {
        log.info("检查长时间未接单的订单：{}", LocalDateTime.now());

        LocalDateTime time = LocalDateTime.now().plusMinutes(-10);

        // 查询待接单且超过10分钟的订单（状态为2：待接单）
        List<Orders> ordersList = orderMapper.getByStatusAndOrderTimeLT(Orders.TO_BE_CONFIRMED, time);

        if (ordersList != null && ordersList.size() > 0) {
            for (Orders orders : ordersList) {
                log.warn("订单长时间未接单，需要提醒商家：订单号={}, 下单时间={}",
                        orders.getNumber(), orders.getOrderTime());

                // 通过WebSocket向客户端浏览器推送消息
                Map map = new HashMap();
                map.put("type", 2); // 1表示来单提醒 2表示客户催单
                map.put("orderId", orders.getId());
                map.put("content", "订单号：" + orders.getNumber() + " 长时间未接单，请及时处理！");

                String json = JSON.toJSONString(map);
                webSocketServer.sendToAllClient(json);
            }
        }
    }
}
