package com.sky.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * Redis配置类 - 最简单的二进制序列化配置
 *
 * 使用最原始的二进制序列化方式：
 * - 所有数据都使用JDK默认的二进制序列化
 * - 配置极简，无需复杂的JSON或其他序列化器配置
 *
 * 优点：
 * - 配置简单，一行代码搞定
 * - 支持所有实现了Serializable接口的Java对象
 * - 性能较好，序列化速度快
 * - 规则未定时的最佳临时方案
 *
 * 缺点：
 * - Redis中存储的是二进制数据，无法直接查看内容
 * - 序列化后的数据相对较大
 * - 需要注意Java版本兼容性
 *
 * 注意：确保所有要存储的对象都实现了Serializable接口
 */
@Configuration
@Slf4j
public class RedisConfiguration {

    /**
     * 配置RedisTemplate - 使用最简单的二进制序列化
     *
     * 不设置任何自定义序列化器，使用Spring Data Redis的默认配置：
     * - 默认使用JDK的二进制序列化（JdkSerializationRedisSerializer）
     * - 所有的key、value、hashKey、hashValue都使用相同的序列化方式
     *
     * @param connectionFactory Redis连接工厂
     * @return RedisTemplate 使用默认二进制序列化的Redis模板
     */
    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        log.info("配置最简单的Redis二进制序列化器");

        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(connectionFactory);

        // 不设置任何序列化器，使用Spring Data Redis的默认配置
        // 默认会使用JdkSerializationRedisSerializer进行二进制序列化
        // 这是最简单的配置方式，一行序列化配置都不需要写

        // 初始化RedisTemplate
        redisTemplate.afterPropertiesSet();

        log.info("Redis序列化器配置完成 - 使用JDK默认二进制序列化");
        return redisTemplate;
    }
}