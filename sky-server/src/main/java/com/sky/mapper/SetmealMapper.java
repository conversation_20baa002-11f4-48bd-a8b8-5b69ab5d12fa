package com.sky.mapper;

import com.sky.annotation.Autofill;
import com.sky.dto.SetmealDTO;
import com.sky.dto.SetmealPageQueryDTO;
import com.sky.entity.Setmeal;
import com.sky.entity.SetmealDish;
import com.sky.enumeration.OperationType;

import com.sky.vo.DishItemVO;
import com.sky.vo.SetmealVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 套餐映射器接口
 * 定义套餐相关的数据库操作方法
 * 包括套餐的增删改查、状态管理和套餐菜品关联管理等功能
 */
@Mapper
public interface SetmealMapper {

    /**
     * 根据菜品ID列表查询关联的套餐ID列表
     * 用于删除菜品前检查是否有套餐关联该菜品
     *
     * @param ids 菜品ID列表
     * @return List<Long> 关联的套餐ID列表
     */
    List<Long> getSetmealIdsByDishIds(List<Long> ids);

    /**
     * 根据分类ID统计套餐数量
     * 统计指定分类下的套餐总数，用于删除分类前的关联检查
     *
     * @param id 分类ID
     * @return Integer 该分类下的套餐数量
     */
    @Select("select count(id) from setmeal where category_id = #{categoryId}")
    Integer countByCategoryId(Long id);

    /**
     * 插入套餐
     * 向数据库中添加新的套餐记录
     * 使用@Autofill注解自动填充创建时间、更新时间、创建人、更新人等公共字段
     *
     * @param setmeal 套餐实体对象，包含套餐的基本信息
     */
    @Autofill(value= OperationType.INSERT)
    void insert(Setmeal setmeal);

    /**
     * 套餐分页查询
     * 根据查询条件进行套餐的分页查询
     * 支持按套餐名称、分类、状态等条件筛选
     *
     * @param setmealPageQueryDTO 套餐分页查询条件对象
     * @return Page<SetmealVO> 分页查询结果
     */
    Page<SetmealVO> pageQuery(SetmealPageQueryDTO setmealPageQueryDTO);

    /**
     * 修改套餐状态
     * 更新套餐的销售状态，可以设置为起售或停售
     *
     * @param status 套餐状态，1表示起售，0表示停售
     * @param id 套餐ID
     */
    @Update("UPDATE  setmeal set status=#{status} where id=#{id}" )
    void startorStop(Integer status, Long id);

    /**
     * 根据ID查询套餐基本信息
     * 查询指定套餐的基本信息，用于删除前的状态检查
     *
     * @param id 套餐ID
     * @return Setmeal 套餐实体对象
     */
    @Select("select * from setmeal where id=#{id}")
    Setmeal queryDishById(Long id);

    /**
     * 批量删除套餐菜品关联
     * 根据套餐ID列表删除套餐与菜品的关联关系
     *
     * @param ids 套餐ID列表
     */
    void deletesetmealdish(List<Long> ids);

    /**
     * 批量删除套餐
     * 根据套餐ID列表批量删除套餐记录
     *
     * @param ids 套餐ID列表
     */
    void deletesetmeal(List<Long> ids);

    /**
     * 根据ID查询套餐详情
     * 查询指定套餐的详细信息，用于套餐信息回显
     *
     * @param id 套餐ID
     * @return Setmeal 套餐实体对象
     */
    @Select("select * from setmeal where id=#{id}")
    Setmeal queryById(Long id);

    /**
     * 根据套餐ID查询套餐菜品关联
     * 查询指定套餐包含的所有菜品关联信息
     *
     * @param id 套餐ID
     * @return List<SetmealDish> 套餐菜品关联列表
     */
    @Select("select * from setmeal_dish where setmeal_id=#{id}")
    List<SetmealDish> queryByIdsetmealdish(Long id);

    /**
     * 更新套餐信息
     * 根据套餐ID更新套餐的基本信息
     * 使用@Autofill注解自动填充更新时间和更新人字段
     *
     * @param setmeal 套餐实体对象，包含要更新的套餐信息
     */
    @Autofill(value = OperationType.UPDATE)
    void updatesetmeal(Setmeal setmeal);

    /**
     * 根据套餐ID查询包含的菜品列表
     * 查询指定套餐包含的所有菜品信息，包含菜品名称、份数、图片、描述等
     * 用于用户端查看套餐详情
     *
     * @param setmealId 套餐ID
     * @return List<DishItemVO> 套餐包含的菜品列表
     */
    @Select("select sd.name, sd.copies, d.image, d.description " +
            "from setmeal_dish sd left join dish d on sd.dish_id = d.id " +
            "where sd.setmeal_id = #{setmealId}")
    List<DishItemVO> getDishItemBySetmealId(Long setmealId);

    /**
     * 条件查询套餐列表
     * 根据套餐条件查询套餐列表，支持按套餐名称、分类ID、状态等条件筛选
     * 主要用于用户端展示套餐信息
     *
     * @param setmeal 套餐查询条件对象，包含分类ID、状态等查询条件
     * @return List<Setmeal> 符合条件的套餐列表
     */
    List<Setmeal> list(Setmeal setmeal);
}
