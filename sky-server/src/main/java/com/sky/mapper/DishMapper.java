package com.sky.mapper;

import com.sky.annotation.Autofill;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.enumeration.OperationType;
import com.sky.vo.DishVO;
import org.apache.ibatis.annotations.Mapper;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 菜品映射器接口
 * 定义菜品相关的数据库操作方法
 * 包括菜品的增删改查和状态管理等功能
 */
@Mapper
public interface DishMapper {

    /**
     * 插入菜品
     * 向数据库中添加新的菜品记录
     * 使用@Autofill注解自动填充创建时间、更新时间、创建人、更新人等公共字段
     *
     * @param dish 菜品实体对象，包含菜品的基本信息
     */
    @Autofill(value = OperationType.INSERT)
    void insert(Dish dish);

    /**
     * 根据分类ID统计菜品数量
     * 统计指定分类下的菜品总数，用于删除分类前的关联检查
     *
     * @param id 分类ID
     * @return Integer 该分类下的菜品数量
     */
    Integer countByCategoryId(Long id);

    /**
     * 菜品分页查询
     * 根据查询条件进行菜品的分页查询
     * 支持按菜品名称、分类、状态等条件筛选
     *
     * @param dishPageQueryDTO 菜品分页查询条件对象
     * @return Page<DishVO> 分页查询结果
     */
    Page<DishVO> pageQuery(DishPageQueryDTO dishPageQueryDTO);

    /**
     * 根据ID查询菜品基本信息
     * 查询指定菜品的基本信息，不包含口味信息
     *
     * @param id 菜品ID
     * @return Dish 菜品实体对象
     */
    @Select("select * from dish where id = #{id}")
    Dish queryInfo(Long id);

    /**
     * 批量删除菜品
     * 根据菜品ID列表批量删除菜品记录
     *
     * @param ids 要删除的菜品ID列表
     */
    void deleteBatch(List<Long> ids);

    /**
     * 根据ID查询菜品详情
     * 查询指定菜品的详细信息，包含菜品基本信息和口味信息
     * 用于菜品信息回显和详情展示
     *
     * @param id 菜品ID
     * @return DishVO 菜品视图对象，包含菜品基本信息和口味列表
     */
    DishVO queryDish(Long id);

    /**
     * 更新菜品信息
     * 根据菜品ID更新菜品的基本信息
     * 使用@Autofill注解自动填充更新时间和更新人字段
     *
     * @param dish 菜品实体对象，包含要更新的菜品信息
     */
    @Autofill(value = OperationType.UPDATE)
    void update(Dish dish);

    /**
     * 根据分类ID查询起售菜品列表
     * 查询指定分类下所有起售状态的菜品
     * 主要用于用户端菜品列表展示
     *
     * @param categoryId 分类ID
     * @return List<Dish> 起售状态的菜品列表
     */
    @Select("select * from dish where category_id=#{categoryId} and status=1")
    List<Dish> list(Long categoryId);

    /**
     * 根据菜品条件查询菜品列表
     * 根据菜品对象中的条件查询菜品列表，支持多条件查询
     * 主要用于条件查询菜品和口味功能
     *
     * @param dish 菜品查询条件对象，包含分类ID、状态等查询条件
     * @return List<Dish> 符合条件的菜品列表
     */
    List<Dish> listByCondition(Dish dish);

    /**
     * 修改菜品状态
     * 更新菜品的销售状态，可以设置为起售或停售
     *
     * @param status 菜品状态，"1"表示起售，"0"表示停售
     * @param id 菜品ID
     */
   @Update("UPDATE  dish set  status=#{status} where id=#{id}")
    void status(String status,Integer id);
}
