package com.sky.mapper;

import com.sky.entity.SetmealDish;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 套餐菜品关联映射器接口
 * 定义套餐与菜品关联关系的数据库操作方法
 * 包括套餐菜品关联的增删改查等功能
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-01-01
 */
@Mapper
public interface SetmealDishMapper {

    /**
     * 批量插入套餐菜品关联
     * 向数据库中批量添加套餐与菜品的关联记录
     * 用于新增套餐时同时添加多个菜品关联信息
     *
     * @param setmealDishes 套餐菜品关联列表，包含套餐ID、菜品ID、菜品名称、价格、份数等信息
     */
    void insertBatch(List<SetmealDish> setmealDishes);
}
