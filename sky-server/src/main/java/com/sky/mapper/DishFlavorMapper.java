package com.sky.mapper;

import com.sky.entity.DishFlavor;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 菜品口味映射器接口
 * 定义菜品口味相关的数据库操作方法
 * 包括口味的增删改查等功能
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-01-01
 */
@Mapper
public interface DishFlavorMapper {

    /**
     * 批量插入菜品口味
     * 向数据库中批量添加菜品口味记录
     * 用于新增菜品时同时添加多个口味信息
     *
     * @param flavors 菜品口味列表，包含口味名称、口味值等信息
     */
    void insertBatch(List<DishFlavor> flavors);

    /**
     * 根据菜品ID批量删除口味数据
     * 批量删除指定菜品列表的所有口味信息
     * 主要用于批量删除菜品时清理关联的口味数据
     *
     * @param dishIds 菜品ID列表，要删除口味的菜品的唯一标识列表
     */
    void deleteByDishIds(List<Long> dishIds);

    /**
     * 根据菜品ID查询口味数据
     * 查询指定菜品的所有口味信息
     * 用于菜品详情展示和编辑时的数据回显
     *
     * @param dishId 菜品ID，要查询口味的菜品的唯一标识
     * @return List<DishFlavor> 菜品口味列表，包含口味名称、口味值等信息
     */
    List<DishFlavor> getByDishId(Long dishId);

    /**
     * 根据菜品ID删除口味数据
     * 删除指定菜品的所有口味信息
     * 主要用于修改菜品时先删除原有口味，再插入新口味
     *
     * @param dishId 菜品ID，要删除口味的菜品的唯一标识
     */
    void deleteByDishId(Long dishId);
}
