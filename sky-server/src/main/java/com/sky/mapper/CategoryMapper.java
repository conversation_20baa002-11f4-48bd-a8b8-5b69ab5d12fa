package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.annotation.Autofill;
import com.sky.enumeration.OperationType;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.entity.Category;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 分类映射器接口
 * 定义分类相关的数据库操作方法
 * 包括菜品分类和套餐分类的增删改查和状态管理等功能
 */
@Mapper
public interface CategoryMapper {

    /**
     * 插入分类数据
     * 向数据库中添加新的分类记录
     * 使用@Autofill注解自动填充创建时间、更新时间、创建人、更新人等公共字段
     *
     * @param category 分类实体对象，包含分类的基本信息
     */
    @Autofill(value=OperationType.INSERT)
    @Insert("insert into category(type, name, sort, status, create_time, update_time, create_user, update_user)" +
            " VALUES" +
            " (#{type}, #{name}, #{sort}, #{status}, #{createTime}, #{updateTime}, #{createUser}, #{updateUser})")
    void insert(Category category);

    /**
     * 分类分页查询
     * 根据查询条件进行分类的分页查询
     * 支持按分类名称、类型等条件筛选
     *
     * @param categoryPageQueryDTO 分类分页查询条件对象，包含页码、页面大小、分类名称、类型等查询条件
     * @return Page<Category> 分页查询结果
     */
    Page<Category> pageQuery(CategoryPageQueryDTO categoryPageQueryDTO);

    /**
     * 根据ID删除分类
     * 删除指定ID的分类记录
     * 删除前需要确保该分类没有被菜品或套餐关联
     *
     * @param id 分类ID，要删除的分类的唯一标识
     */
    @Delete("delete from category where id = #{id}")
    void deleteById(Long id);

    /**
     * 根据ID修改分类信息
     * 更新分类的基本信息，如分类名称、排序、状态等
     * 使用@Autofill注解自动填充更新时间和更新人字段
     *
     * @param category 分类实体对象，包含要更新的分类信息
     */
    @Autofill(value=OperationType.UPDATE)
    void update(Category category);

    /**
     * 根据类型查询分类列表
     * 查询指定类型的所有启用状态的分类，按排序字段排序
     * 用于下拉选择框和分类导航展示
     *
     * @param type 分类类型，1表示菜品分类，2表示套餐分类，null表示查询所有类型
     * @return List<Category> 符合条件的分类列表
     */
    List<Category> list(Integer type);
}
