package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.annotation.Autofill;
import com.sky.dto.EmployeePageQueryDTO;
import com.sky.entity.Employee;
import com.sky.enumeration.OperationType;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface EmployeeMapper {

    /**
     * 根据用户名查询员工
     * 用于员工登录时验证用户名是否存在
     *
     * @param username 员工用户名
     * @return Employee 员工实体对象，如果不存在则返回null
     */
    @Select("select * from employee where username = #{username}")
    Employee getByUsername(String username);

    /**
     * 插入新员工
     * 向数据库中添加新的员工记录
     * 使用@Autofill注解自动填充创建时间、更新时间、创建人、更新人等公共字段
     *
     * @param employee 员工实体对象，包含员工的基本信息
     */
    @Autofill(value = OperationType.INSERT)
    @Insert("INSERT into employee (name, username, password, phone, sex, id_number, create_time, update_time, create_user, update_user) " +
            "VALUES (#{name}, #{username}, #{password}, #{phone}, #{sex}, #{idNumber}, #{createTime}, #{updateTime}, #{createUser}, #{updateUser})")
    void insert(Employee employee);

    /**
     * 员工分页查询
     * 根据查询条件进行员工信息的分页查询
     * 支持按员工姓名进行模糊查询
     *
     * @param employeePageQueryDTO 员工分页查询条件对象
     * @return Page<Employee> 分页查询结果
     */
    com.github.pagehelper.Page<Employee> pageQuery(EmployeePageQueryDTO employeePageQueryDTO);

    /**
     * 更新员工信息
     * 根据员工ID更新员工的基本信息
     * 支持动态更新，只更新非空字段
     *
     * @param employee 员工实体对象，包含要更新的员工信息
     */
    void update(Employee employee);

    /**
     * 根据ID查询员工信息
     * 用于员工信息回显，查询指定员工的详细信息
     *
     * @param id 员工ID
     * @return Employee 员工实体对象
     */
    @Select("select * from employee where id=#{id}")
    Employee queryInfo(Long id);

    /**
     * 查询所有员工列表
     * 获取系统中所有员工的基本信息，按创建时间倒序排列
     * 主要用于下拉选择框等场景
     *
     * @return List<Employee> 所有员工的列表
     */
    @Select("select * from employee order by create_time desc")
    List<Employee> list();
}
