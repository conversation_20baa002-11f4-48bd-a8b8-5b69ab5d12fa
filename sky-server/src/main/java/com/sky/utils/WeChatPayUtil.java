package com.sky.utils;

import com.alibaba.fastjson.JSONObject;
import com.sky.properties.WeChatProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * 微信支付工具类（模拟版本）
 * 
 * 注意：这是一个模拟版本，用于开发测试
 * 实际生产环境需要使用真实的微信支付SDK
 */
@Component
@Slf4j
public class WeChatPayUtil {

    @Autowired
    private WeChatProperties weChatProperties;

    /**
     * 发起微信支付
     * 
     * @param orderNum 商户订单号
     * @param total 支付金额，单位：元
     * @param description 商品描述
     * @param openid 微信用户的openid
     * @return 支付参数
     */
    public JSONObject pay(String orderNum, BigDecimal total, String description, String openid) throws Exception {
        log.info("发起微信支付请求：订单号={}, 金额={}, 描述={}, openid={}", orderNum, total, description, openid);
        
        // 模拟微信支付接口调用
        // 实际环境中这里会调用微信支付的JSAPI接口
        
        JSONObject jsonObject = new JSONObject();
        
        // 模拟微信支付返回的参数
        String nonceStr = UUID.randomUUID().toString().replace("-", "");
        String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
        String packageStr = "prepay_id=" + UUID.randomUUID().toString().replace("-", "");
        String signType = "RSA";
        String paySign = generateMockPaySign(nonceStr, timeStamp, packageStr);
        
        jsonObject.put("nonceStr", nonceStr);
        jsonObject.put("timeStamp", timeStamp);
        jsonObject.put("package", packageStr);
        jsonObject.put("signType", signType);
        jsonObject.put("paySign", paySign);
        
        log.info("微信支付参数生成成功：{}", jsonObject);
        
        return jsonObject;
    }
    
    /**
     * 生成模拟的支付签名
     * 实际环境中需要按照微信支付的签名规则生成
     */
    private String generateMockPaySign(String nonceStr, String timeStamp, String packageStr) {
        // 这里是模拟签名，实际环境需要按照微信支付的签名算法
        String signData = weChatProperties.getAppid() + timeStamp + nonceStr + packageStr;
        return "MOCK_SIGN_" + signData.hashCode();
    }
}
