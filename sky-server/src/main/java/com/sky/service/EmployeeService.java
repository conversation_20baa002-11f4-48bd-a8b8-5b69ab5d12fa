package com.sky.service;

import com.sky.dto.EmployeeDTO;
import com.sky.dto.EmployeeLoginDTO;
import com.sky.dto.EmployeePageQueryDTO;
import com.sky.entity.Employee;
import com.sky.result.PageResult;

import java.util.List;

/**
 * 员工服务接口
 * 定义员工管理相关的业务操作方法
 * 包括员工登录、新增、查询、修改、状态管理等功能
 */
public interface EmployeeService {

    /**
     * 员工登录
     * 验证员工登录信息，包括用户名、密码和账号状态的校验
     *
     * @param employeeLoginDTO 员工登录数据传输对象，包含用户名和密码
     * @return Employee 登录成功的员工实体对象
     */
    Employee login(EmployeeLoginDTO employeeLoginDTO);

    /**
     * 新增员工
     * 保存新员工信息到数据库，设置默认密码并进行相关字段的自动填充
     *
     * @param employeeDTO 员工数据传输对象，包含员工基本信息
     */
    void save(EmployeeDTO employeeDTO);

    /**
     * 员工分页查询
     * 根据查询条件进行员工信息的分页查询，支持按员工姓名模糊查询
     *
     * @param employeePageQueryDTO 员工分页查询条件对象
     * @return PageResult 分页查询结果
     */
    PageResult pageQuery(EmployeePageQueryDTO employeePageQueryDTO);

    /**
     * 启用或禁用员工账号
     * 修改员工账号的状态，可以启用或禁用员工账号
     *
     * @param status 员工状态，1表示启用，0表示禁用
     * @param id 员工ID，要修改状态的员工的唯一标识
     */
    void startorstop( Integer status,Long id);

    /**
     * 根据ID查询员工信息
     * 用于员工信息回显，查询指定员工的详细信息
     *
     * @param id 员工ID，要查询的员工的唯一标识
     * @return Employee 员工实体对象
     */
    Employee querInfo(Long id);

    /**
     * 编辑员工信息
     * 更新员工的基本信息，如姓名、用户名、手机号、性别、身份证号等
     *
     * @param employeeDTO 员工数据传输对象，包含要更新的员工信息
     */
    void editEmpInfo(EmployeeDTO employeeDTO);

    /**
     * 获取所有员工列表
     * 查询系统中所有员工的基本信息，不进行分页
     * 主要用于下拉选择框等场景
     *
     * @return List<Employee> 所有员工的列表
     */
    List<Employee> list();
}
