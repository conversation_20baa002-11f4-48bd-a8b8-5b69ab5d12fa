package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.dto.DishDTO;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.entity.DishFlavor;
import com.sky.mapper.DishFlavorMapper;
import com.sky.mapper.DishMapper;
import com.sky.mapper.SetmealMapper;
import com.sky.result.PageResult;
import com.sky.service.DishService;
import com.sky.vo.DishVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class DishServiceImpl implements DishService {
    @Autowired
    private DishMapper dishMapper;

    @Autowired
    private DishFlavorMapper dishFlavorMapper;

    @Autowired
    private SetmealMapper setmealMapper;

    /**
     * 新增菜品
     * 保存菜品基本信息和口味信息到数据库
     * 使用事务保证菜品和口味数据的一致性
     *
     * @param dishDTO 菜品数据传输对象，包含菜品基本信息和口味列表
     * @throws RuntimeException 当数据库操作失败时抛出异常
     */
    @Transactional
    @Override
    public void save(DishDTO dishDTO) {
        Dish dish = new Dish();
        BeanUtils.copyProperties(dishDTO, dish);
        dishMapper.insert(dish);

        Long dishId = dish.getId();

        if (dishDTO.getFlavors() != null && !dishDTO.getFlavors().isEmpty()) {
            List<DishFlavor> flavors = dishDTO.getFlavors();
            flavors.forEach(flavor -> flavor.setDishId(dishId));
            dishFlavorMapper.insertBatch(flavors);
        }
    }

    /**
     * 菜品分页查询
     * 根据查询条件进行菜品的分页查询，支持按菜品名称、分类、状态等条件筛选
     * 使用PageHelper插件实现分页功能
     *
     * @param dishPageQueryDTO 菜品分页查询条件对象，包含页码、页面大小、菜品名称、分类ID、状态等查询条件
     * @return PageResult 分页查询结果，包含总记录数和当前页菜品数据列表
     */
    @Override
    public PageResult pageQuery(DishPageQueryDTO dishPageQueryDTO) {
        PageHelper.startPage(dishPageQueryDTO.getPage(), dishPageQueryDTO.getPageSize());
        Page<DishVO> page = dishMapper.pageQuery(dishPageQueryDTO);
        return new PageResult(page.getTotal(), page.getResult());
    }

    /**
     * 批量删除菜品
     * 根据菜品ID列表批量删除菜品，删除前会进行多项业务规则校验
     * 包括菜品存在性、销售状态、套餐关联等检查
     * 删除操作包括删除菜品基本信息和关联的口味信息
     * 使用事务保证数据一致性
     *
     * @param ids 要删除的菜品ID列表
     * @throws RuntimeException 当菜品不存在、正在售卖或关联套餐时抛出异常
     */
    @Transactional
    @Override
    public void deleteBatch(List<Long> ids) {
        for (Long id : ids) {
            Dish dish = dishMapper.queryInfo(id);
            if (dish == null) {
                throw new RuntimeException("菜品ID " + id + " 不存在，无法删除");
            }
            if (dish.getStatus() == 1) {
                throw new RuntimeException("菜品 " + dish.getName() + " 正在售卖中，不能删除");
            }
        }

        List<Long> setmealIds = setmealMapper.getSetmealIdsByDishIds(ids);
        if (setmealIds != null && setmealIds.size() > 0) {
            throw new RuntimeException("当前菜品关联了套餐,不能删除");
        }

        // 删除菜品关联的口味数据
        dishFlavorMapper.deleteByDishIds(ids);

        // 批量删除菜品
        dishMapper.deleteBatch(ids);
    }

    /**
     * 根据ID查询菜品详情
     * 用于菜品信息回显，一次性查询菜品基本信息和口味信息
     * 主要用于编辑菜品时的数据回显功能
     *
     * @param id 菜品ID，要查询的菜品的唯一标识
     * @return DishVO 菜品视图对象，包含菜品基本信息和口味列表
     */
    @Override
    public DishVO queryById(Long id) {
        // 一次查询获取菜品信息和口味信息
        DishVO dishVO = dishMapper.queryDish(id);
        return dishVO;
    }

    /**
     * 修改菜品
     * 更新菜品基本信息和口味信息
     * 采用先删除后插入的方式更新菜品口味，确保数据的准确性
     * 使用事务保证数据一致性
     *
     * @param dishDTO 菜品数据传输对象，包含要更新的菜品信息和口味列表
     * @throws RuntimeException 当数据库操作失败时抛出异常
     */
    @Transactional
    @Override
    public void update(DishDTO dishDTO) {
        // 1. 更新菜品基本信息
        Dish dish = new Dish();
        BeanUtils.copyProperties(dishDTO, dish);
        dishMapper.update(dish);

        // 2. 处理菜品口味更新
        Long dishId = dishDTO.getId();

        // 先删除原有的口味数据
        dishFlavorMapper.deleteByDishId(dishId);

        // 再插入新的口味数据
        if (dishDTO.getFlavors() != null && !dishDTO.getFlavors().isEmpty()) {
            List<DishFlavor> flavors = dishDTO.getFlavors();
            flavors.forEach(flavor -> flavor.setDishId(dishId));
            dishFlavorMapper.insertBatch(flavors);
        }
    }

    /**
     * 根据分类ID查询菜品列表
     * 查询指定分类下的所有菜品信息
     * 主要用于管理端菜品列表展示
     *
     * @param categoryId 分类ID，要查询菜品的分类的唯一标识
     * @return List<Dish> 指定分类下的菜品列表
     */
    @Override
    public List<Dish> list(Long categoryId) {
        List<Dish>list=dishMapper.list(categoryId);
        return  list;
    }

    /**
     * 菜品起售停售
     * 修改菜品的销售状态，可以将菜品设置为起售或停售状态
     *
     * @param status 菜品状态，"1"表示起售，"0"表示停售
     * @param id 菜品ID，要修改状态的菜品的唯一标识
     */
    @Override
    public void status(String status, Integer id) {
        dishMapper.status(status,id);
    }

    /**
     * 条件查询菜品和口味
     * 根据菜品条件查询菜品列表，并同时查询每个菜品的口味信息
     * 主要用于用户端展示菜品信息，包含完整的菜品和口味数据
     *
     * @param dish 菜品查询条件对象，包含分类ID、状态等查询条件
     * @return List<DishVO> 符合条件的菜品列表，每个菜品包含口味信息
     */
    public List<DishVO> listWithFlavor(Dish dish) {
        List<Dish> dishList = dishMapper.listByCondition(dish);

        List<DishVO> dishVOList = new ArrayList<>();

        for (Dish d : dishList) {
            DishVO dishVO = new DishVO();
            BeanUtils.copyProperties(d,dishVO);

            //根据菜品id查询对应的口味
            List<DishFlavor> flavors = dishFlavorMapper.getByDishId(d.getId());

            dishVO.setFlavors(flavors);
            dishVOList.add(dishVO);
        }

        return dishVOList;
    }
}