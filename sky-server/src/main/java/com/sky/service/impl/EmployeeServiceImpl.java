package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.constant.MessageConstant;
import com.sky.constant.StatusConstant;
import com.sky.context.BaseContext;
import com.sky.dto.EmployeeDTO;
import com.sky.dto.EmployeeLoginDTO;
import com.sky.dto.EmployeePageQueryDTO;
import com.sky.entity.Employee;
import com.sky.exception.AccountLockedException;
import com.sky.exception.AccountNotFoundException;
import com.sky.exception.PasswordErrorException;
import com.sky.mapper.EmployeeMapper;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.EmployeeService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.GetMapping;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class EmployeeServiceImpl implements EmployeeService {

    @Autowired
    private EmployeeMapper employeeMapper;

    /**
     * 新增员工
     * 保存新员工信息到数据库，设置默认密码为123456并进行MD5加密
     * 新员工默认状态为启用状态，自动设置创建时间和更新时间
     *
     * @param employeeDTO 员工数据传输对象，包含员工基本信息
     * @throws RuntimeException 当数据库操作失败时抛出异常
     */
    @Override
    public void save(EmployeeDTO employeeDTO) {
        System.out.println("当前线程id"+Thread.currentThread().getId());
        Employee employee=new Employee();
        BeanUtils.copyProperties(employeeDTO,employee);
        employee.setStatus(StatusConstant.ENABLE);
        employee.setPassword(DigestUtils.md5DigestAsHex("123456".getBytes()));
        //
        employee.setCreateTime(LocalDateTime.now());
        employee.setUpdateTime(LocalDateTime.now());

        employee.setCreateUser(BaseContext.getCurrentId());
        employee.setUpdateUser(BaseContext.getCurrentId());
        employeeMapper.insert(employee);
    }


    /**
     * 员工登录
     * 验证员工登录信息，包括用户名、密码和账号状态的校验
     * 密码使用MD5加密后进行比对验证
     *
     * @param employeeLoginDTO 员工登录数据传输对象，包含用户名和密码
     * @return Employee 登录成功的员工实体对象
     * @throws AccountNotFoundException 当用户名不存在时抛出此异常
     * @throws PasswordErrorException 当密码错误时抛出此异常
     * @throws AccountLockedException 当账号被锁定时抛出此异常
     */
    public Employee login(EmployeeLoginDTO employeeLoginDTO) {
        String username = employeeLoginDTO.getUsername();
        String password = employeeLoginDTO.getPassword();

        //1、根据用户名查询数据库中的数据
        Employee employee = employeeMapper.getByUsername(username);

        //2、处理各种异常情况（用户名不存在、密码不对、账号被锁定）
        if (employee == null) {
            //账号不存在
            throw new AccountNotFoundException(MessageConstant.ACCOUNT_NOT_FOUND);
        }

        //密码比对
        // TODO 后期需要进行md5加密，然后再进行比对
        password = DigestUtils.md5DigestAsHex(password.getBytes());
        if (!password.equals(employee.getPassword())) {
            //密码错误
            throw new PasswordErrorException(MessageConstant.PASSWORD_ERROR);
        }

        if (employee.getStatus() == StatusConstant.DISABLE) {
            //账号被锁定
            throw new AccountLockedException(MessageConstant.ACCOUNT_LOCKED);
        }

        //3、返回实体对象
        return employee;
    }

    /**
     * 员工分页查询
     * 根据查询条件进行员工信息的分页查询，支持按员工姓名模糊查询
     * 使用PageHelper插件实现分页功能
     *
     * @param employeePageQueryDTO 员工分页查询条件对象，包含页码、页面大小、员工姓名等查询条件
     * @return PageResult 分页查询结果，包含总记录数和当前页员工数据列表
     */
    @Override
    public PageResult pageQuery(EmployeePageQueryDTO employeePageQueryDTO) {
        PageHelper.startPage(employeePageQueryDTO.getPage(), employeePageQueryDTO.getPageSize());
        Page<Employee> page = employeeMapper.pageQuery(employeePageQueryDTO);

        long total = page.getTotal();
        List<Employee> records = page.getResult();

        return new PageResult(total, records);
    }

    /**
     * 启用或禁用员工账号
     * 修改员工账号的状态，可以启用或禁用员工账号
     * 自动设置更新时间和更新人信息
     *
     * @param status 员工状态，1表示启用，0表示禁用
     * @param id 员工ID，要修改状态的员工的唯一标识
     */
    @Override
    public void startorstop(Integer status, Long id) {
        Employee employee = Employee.builder()
                .id(id)
                .status(status)
                .updateTime(LocalDateTime.now())
                .updateUser(BaseContext.getCurrentId())
                .build();
        employeeMapper.update(employee);
    }

    /**
     * 根据ID查询员工信息
     * 用于员工信息回显，查询指定员工的详细信息
     * 主要用于编辑员工信息时的数据回显功能
     *
     * @param id 员工ID，要查询的员工的唯一标识
     * @return Employee 员工实体对象，包含员工的详细信息
     */
    @Override
    public Employee querInfo(Long id) {
        return employeeMapper.queryInfo(id);
    }

    /**
     * 编辑员工信息
     * 更新员工的基本信息，如姓名、用户名、手机号、性别、身份证号等
     * 自动设置更新时间和更新人信息
     *
     * @param employeeDTO 员工数据传输对象，包含要更新的员工信息
     */
    @Override
    public void editEmpInfo(EmployeeDTO employeeDTO) {
        Employee employee = new Employee();
        BeanUtils.copyProperties(employeeDTO, employee);
        employee.setUpdateTime(LocalDateTime.now());
        employee.setUpdateUser(BaseContext.getCurrentId());
        employeeMapper.update(employee);
    }

    /**
     * 获取所有员工列表
     * 查询系统中所有员工的基本信息，不进行分页
     * 主要用于下拉选择框等场景
     *
     * @return List<Employee> 所有员工的列表
     */
    @Override
    public List<Employee> list() {
        return employeeMapper.list();
    }
}
