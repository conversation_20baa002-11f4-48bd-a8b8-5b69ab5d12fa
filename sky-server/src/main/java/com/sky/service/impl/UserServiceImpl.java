package com.sky.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sky.constant.MessageConstant;
import com.sky.dto.UserLoginDTO;
import com.sky.entity.User;
import com.sky.exception.LoginFailedException;
import com.sky.mapper.UserMapper;
import com.sky.properties.WeChatProperties;
import com.sky.service.UserService;
import com.sky.utils.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户服务实现类
 * 实现用户相关的业务逻辑
 * 主要处理微信用户的登录和自动注册功能
 */
@Service
@Slf4j
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private WeChatProperties weChatProperties;

    /**
     * 微信登录接口地址
     */
    public static final String WX_LOGIN_URL = "https://api.weixin.qq.com/sns/jscode2session";

    /**
     * 用户登录
     * 通过微信授权码获取用户openid，实现用户登录
     * 如果用户不存在则自动注册新用户
     *
     * @param userLoginDTO 用户登录数据传输对象，包含微信授权码
     * @return User 登录成功的用户实体对象
     * @throws LoginFailedException 当微信登录失败时抛出此异常
     */
    @Override
    public User login(UserLoginDTO userLoginDTO) {
        log.info("用户微信登录，授权码：{}", userLoginDTO.getCode());

        // 1. 调用微信接口服务，获取当前微信用户的openid
        String openid = getOpenidFromWeChat(userLoginDTO.getCode());

        // 2. 判断openid是否为空，如果为空表示登录失败，抛出业务异常
        if (openid == null) {
            throw new LoginFailedException(MessageConstant.LOGIN_FAILED);
        }

        // 3. 判断当前用户是否为新用户
        User user = userMapper.queryByID(openid);

        // 4. 如果是新用户，自动完成注册
        if (user == null) {
            user = User.builder()
                    .openid(openid)
                    .createTime(LocalDateTime.now())
                    .build();
            userMapper.insert(user);
            log.info("新用户自动注册，openid：{}", openid);

            // 重新查询获取用户ID
            user = userMapper.queryByID(openid);
        }

        log.info("用户登录成功，用户ID：{}", user.getId());
        return user;
    }

    /**
     * 调用微信接口获取用户openid
     *
     * @param code 微信授权码
     * @return String 用户的openid，获取失败返回null
     */
    private String getOpenidFromWeChat(String code) {
        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("appid", weChatProperties.getAppid());
        params.put("secret", weChatProperties.getSecret());
        params.put("js_code", code);
        params.put("grant_type", "authorization_code");

        // 调用微信接口
        String response = HttpClientUtil.doGet(WX_LOGIN_URL, params);
        log.info("微信登录接口响应：{}", response);

        // 解析响应结果
        JSONObject jsonObject = JSON.parseObject(response);
        return jsonObject.getString("openid");
    }
}
