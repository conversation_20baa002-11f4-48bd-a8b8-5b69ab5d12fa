package com.sky.service.impl;

import com.sky.context.BaseContext;
import com.sky.dto.ShoppingCartDTO;
import com.sky.entity.Dish;
import com.sky.entity.Setmeal;
import com.sky.entity.ShoppingCart;
import com.sky.mapper.DishMapper;
import com.sky.mapper.SetmealMapper;
import com.sky.mapper.ShoppingCartMapper;
import com.sky.service.ShoppingCartService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 购物车业务实现类
 *
 * 主要功能：
 * 1. 添加商品到购物车（支持菜品和套餐）
 * 2. 减少购物车中商品数量
 * 3. 清空用户购物车
 * 4. 查看购物车列表
 *
 * 设计思路：
 * - 每个用户的购物车项目存储在shopping_cart表中
 * - 同一商品+口味组合只存储一条记录，通过number字段记录数量
 * - 支持菜品和套餐两种商品类型
 * - 使用ThreadLocal获取当前登录用户ID，确保数据隔离
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-01-01
 */
@Service
public class ShoppingCartServiceImpl implements ShoppingCartService {

    /**
     * 菜品数据访问层
     * 用于获取菜品的详细信息（名称、价格、图片等）
     */
    @Autowired
    private DishMapper dishMapper;

    /**
     * 购物车数据访问层
     * 负责购物车相关的数据库操作
     */
    @Autowired
    private ShoppingCartMapper shoppingCartMapper;

    /**
     * 套餐数据访问层
     * 用于获取套餐的详细信息（名称、价格、图片等）
     */
    @Autowired
    private SetmealMapper setmealMapper;

    /**
     * 添加商品到购物车
     *
     * 业务逻辑：
     * 1. 检查相同商品+口味是否已在购物车中
     * 2. 如果已存在：数量+1，更新记录
     * 3. 如果不存在：获取商品详细信息，新增记录
     *
     * 支持两种商品类型：
     * - 菜品：通过dishId获取菜品信息
     * - 套餐：通过setmealId获取套餐信息
     *
     * @param shoppingCartDTO 购物车数据传输对象，包含商品ID、口味、数量等信息
     */
    @Override
    public void add(ShoppingCartDTO shoppingCartDTO) {
        // 创建购物车实体对象
        ShoppingCart shoppingCart = new ShoppingCart();

        // 复制DTO中的基本信息（dishId、setmealId、dishFlavor等）
        BeanUtils.copyProperties(shoppingCartDTO, shoppingCart);

        // 设置当前登录用户ID（从ThreadLocal获取）
        shoppingCart.setUserId(BaseContext.getCurrentId());

        // 查询当前商品是否已在购物车中
        // 查询条件：用户ID + 商品ID + 口味
        List<ShoppingCart> list = shoppingCartMapper.list(shoppingCart);

        // 如果商品已存在，更新数量
        if(list != null && list.size() > 0) {
            ShoppingCart cart = list.get(0);  // 获取已存在的购物车项
            cart.setNumber(cart.getNumber() + 1);  // 数量+1
            shoppingCartMapper.updateNumberById(cart);  // 更新数据库
            return;  // 直接返回，不执行后续新增逻辑
        }

        // 商品不存在，需要新增记录
        // 获取商品ID，判断是菜品还是套餐
        Long dishId = shoppingCartDTO.getDishId();
        Long setmealId = shoppingCartDTO.getSetmealId();

        if(dishId != null) {
            // 处理菜品：从菜品表获取详细信息
            Dish dish = dishMapper.queryInfo(dishId);
            shoppingCart.setName(dish.getName());        // 菜品名称
            shoppingCart.setImage(dish.getImage());      // 菜品图片
            shoppingCart.setAmount(dish.getPrice());     // 菜品价格
            shoppingCart.setNumber(1);                   // 初始数量为1
            shoppingCart.setCreateTime(dish.getCreateTime()); // 创建时间
        } else {
            // 处理套餐：从套餐表获取详细信息
            Setmeal setmeal = setmealMapper.queryDishById(setmealId);
            shoppingCart.setName(setmeal.getName());     // 套餐名称
            shoppingCart.setImage(setmeal.getImage());   // 套餐图片
            shoppingCart.setAmount(setmeal.getPrice());  // 套餐价格
            shoppingCart.setNumber(1);                   // 初始数量为1
            shoppingCart.setCreateTime(setmeal.getCreateTime()); // 创建时间
        }

        // 将新的购物车项插入数据库
        shoppingCartMapper.insert(shoppingCart);
    }

    @Override
    public List<ShoppingCart> list() {
        ShoppingCart shoppingCart = new ShoppingCart();
        shoppingCart.setUserId(BaseContext.getCurrentId());
        List<ShoppingCart> list = shoppingCartMapper.list(shoppingCart);
        return list;
    }


    /**
     * 减少购物车中商品数量
     *
     * 业务逻辑：
     * 1. 查找指定的购物车商品
     * 2. 将商品数量减1
     * 3. 如果数量减为0，则删除该商品记录
     * 4. 如果数量大于0，则更新数量
     *
     * @param shoppingCartDTO 购物车数据传输对象，包含要减少的商品信息
     */
    @Override
    public void sub(ShoppingCartDTO shoppingCartDTO) {
        // 构建查询条件
        ShoppingCart shoppingCart = new ShoppingCart();
        shoppingCart.setUserId(BaseContext.getCurrentId());
        shoppingCart.setDishId(shoppingCartDTO.getDishId());
        shoppingCart.setSetmealId(shoppingCartDTO.getSetmealId());
        shoppingCart.setDishFlavor(shoppingCartDTO.getDishFlavor());

        // 查询购物车中的商品
        List<ShoppingCart> list = shoppingCartMapper.list(shoppingCart);

        if(list != null && list.size() > 0) {
            ShoppingCart cart = list.get(0);
            Integer number = cart.getNumber() - 1;

            if(number > 0) {
                // 数量大于0，更新数量
                cart.setNumber(number);
                shoppingCartMapper.updateNumberById(cart);
            } else {
                // 数量为0，删除该商品记录
                ShoppingCart deleteCart = new ShoppingCart();
                deleteCart.setId(cart.getId());
                shoppingCartMapper.delete(deleteCart);
            }
        }
    }

    /**
     * 清空用户购物车
     *
     * 业务逻辑：
     * 1. 根据当前登录用户ID删除该用户的所有购物车商品
     * 2. 通常在用户下单成功后调用，清空购物车
     *
     * 使用场景：
     * - 用户手动清空购物车
     * - 订单提交成功后自动清空购物车
     */
    @Override
    public void clean() {
        ShoppingCart shoppingCart = new ShoppingCart();
        shoppingCart.setUserId(BaseContext.getCurrentId());
        shoppingCartMapper.delete(shoppingCart);
    }
}
