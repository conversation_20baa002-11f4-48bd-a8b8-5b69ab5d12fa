package com.sky.service.impl;

import com.github.pagehelper.PageHelper;
import com.sky.constant.MessageConstant;
import com.sky.dto.SetmealDTO;
import com.sky.dto.SetmealPageQueryDTO;
import com.sky.entity.Setmeal;
import com.sky.entity.SetmealDish;
import com.sky.exception.DeletionNotAllowedException;
import com.sky.mapper.SetmealDishMapper;
import com.sky.mapper.SetmealMapper;
import com.sky.result.PageResult;
import com.sky.service.SetmealService;
import com.sky.vo.DishItemVO;
import com.sky.vo.SetmealVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
@Slf4j
@Service
public class SetmealSeviceImpl implements SetmealService {
    @Autowired
    private SetmealMapper setmealMapper;

    @Autowired
    private SetmealDishMapper setmealDishMapper;



    /**
     * 新增套餐
     * 该方法用于保存套餐信息，包括套餐基本信息和套餐菜品关联信息
     * 使用事务保证数据一致性，确保套餐和菜品关联同时成功或失败
     *
     * @param setmealDTO 套餐数据传输对象，包含套餐基本信息和关联菜品列表
     * @throws RuntimeException 当数据库操作失败时抛出异常
     */
    @Transactional
    @Override
    public void save(SetmealDTO setmealDTO) {
        System.out.println("当前线程id"+Thread.currentThread().getId());

        // 1. 插入套餐基本信息
        Setmeal setmeal = new Setmeal();
        BeanUtils.copyProperties(setmealDTO, setmeal);
        setmealMapper.insert(setmeal);

        // 2. 获取插入后的套餐ID
        Long setmealId = setmeal.getId();
        System.out.println("插入套餐后获得的ID: " + setmealId);

        // 3. 插入套餐菜品关联数据
        List<SetmealDish> setmealDishes = setmealDTO.getSetmealDishes();
        if (setmealDishes != null && !setmealDishes.isEmpty()) {
            // 为每个套餐菜品设置套餐ID
            setmealDishes.forEach(setmealDish -> {
                setmealDish.setSetmealId(setmealId);
                System.out.println("设置套餐菜品关联: " + setmealDish);
            });
            setmealDishMapper.insertBatch(setmealDishes);
        } else {
            System.out.println("警告：套餐菜品列表为空！");
        }
    }

    /**
     * 套餐分页查询
     * 根据查询条件进行套餐的分页查询，支持按套餐名称、分类、状态等条件筛选
     *
     * @param setmealPageQueryDTO 分页查询条件对象，包含页码、页面大小、套餐名称、分类ID、状态等查询条件
     * @return PageResult 分页查询结果，包含总记录数和当前页数据列表
     */
    @Override
    public PageResult pageQuery(SetmealPageQueryDTO setmealPageQueryDTO) {
        PageHelper.startPage(setmealPageQueryDTO.getPage(), setmealPageQueryDTO.getPageSize());
        Page<SetmealVO> page = setmealMapper.pageQuery(setmealPageQueryDTO);
        return new PageResult(page.getTotal(), page.getResult());
    }

    /**
     * 套餐起售停售
     * 修改套餐的销售状态，可以将套餐设置为起售或停售状态
     *
     * @param status 套餐状态，1表示起售，0表示停售
     * @param id 套餐ID，要修改状态的套餐的唯一标识
     */
    @Override
    public void startOrStop(Integer status, Long id) {
        setmealMapper.startorStop(status,id);

    }
    /**
     * 批量删除套餐
     * 根据套餐ID列表批量删除套餐，删除前会检查套餐是否处于起售状态
     * 如果套餐正在起售中，则不允许删除，会抛出异常
     * 删除操作包括删除套餐基本信息和套餐菜品关联信息
     *
     * @param ids 要删除的套餐ID列表
     * @throws DeletionNotAllowedException 当套餐处于起售状态时抛出此异常，不允许删除
     */
    @Transactional
    @Override
    public void deleteByIds(List<Long> ids) {
        for (Long id : ids){
            //删除套餐是否在售
            Setmeal setmeal=setmealMapper.queryDishById(id);
            if (setmeal.getStatus()==1){
                throw new DeletionNotAllowedException(MessageConstant.SETMEAL_ON_SALE);
            }

        }
        setmealMapper.deletesetmealdish(ids);
        setmealMapper.deletesetmeal(ids);
    }

    /**
     * 根据ID查询套餐详情
     * 用于套餐信息回显，查询套餐的基本信息和关联的菜品信息
     * 主要用于编辑套餐时的数据回显功能
     *
     * @param id 套餐ID，要查询的套餐的唯一标识
     * @return SetmealVO 套餐视图对象，包含套餐基本信息和关联菜品列表
     */
    @Override
    public SetmealVO queryById(Long id) {
        Setmeal setmeal=new Setmeal();
        setmeal=setmealMapper.queryById(id);
        SetmealVO setmealVO=new SetmealVO();
        BeanUtils.copyProperties(setmeal,setmealVO);
        List<SetmealDish> setmealDishes=setmealMapper.queryByIdsetmealdish(id);
        setmealVO.setSetmealDishes(setmealDishes);
        return setmealVO;
    }
    /**
     * 修改套餐
     * 更新套餐信息，包括套餐基本信息和套餐菜品关联信息
     * 采用先删除后插入的方式更新套餐菜品关联，确保数据的准确性
     * 使用事务保证数据一致性
     *
     * @param setmealDTO 套餐数据传输对象，包含要更新的套餐信息和关联菜品列表
     * @throws RuntimeException 当数据库操作失败时抛出异常
     */
    @Transactional
    @Override
    public void update(SetmealDTO setmealDTO) {
        // 1. 更新套餐基本信息
        Setmeal setmeal = new Setmeal();
        BeanUtils.copyProperties(setmealDTO, setmeal);
        setmealMapper.updatesetmeal(setmeal);

        // 2. 删除原有的套餐菜品关联
        Long setmealId = setmealDTO.getId();
        List<Long> ids = Arrays.asList(setmealId);
        setmealMapper.deletesetmealdish(ids);

        // 3. 重新插入套餐菜品关联
        List<SetmealDish> setmealDishes = setmealDTO.getSetmealDishes();
        if (setmealDishes != null && !setmealDishes.isEmpty()) {
            // 为每个套餐菜品设置套餐ID
            setmealDishes.forEach(setmealDish -> {
                setmealDish.setSetmealId(setmealId);
            });
            setmealDishMapper.insertBatch(setmealDishes);
        }

    }

    /**
     * 条件查询套餐列表
     * 根据套餐条件查询套餐列表，主要用于用户端展示套餐信息
     *
     * @param setmeal 套餐查询条件对象，包含分类ID、状态等查询条件
     * @return List<Setmeal> 符合条件的套餐列表
     */
    public List<Setmeal> list(Setmeal setmeal) {
        List<Setmeal> list = setmealMapper.list(setmeal);
        return list;
    }

    /**
     * 根据套餐ID查询包含的菜品列表
     * 用于用户端查看套餐详情时展示套餐包含的具体菜品信息
     *
     * @param id 套餐ID，要查询菜品的套餐的唯一标识
     * @return List<DishItemVO> 套餐包含的菜品列表，包含菜品名称、图片等信息
     */
    public List<DishItemVO> getDishItemById(Long id) {
        return setmealMapper.getDishItemBySetmealId(id);
    }
}
