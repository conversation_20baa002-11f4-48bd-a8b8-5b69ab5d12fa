package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.constant.MessageConstant;
import com.sky.constant.StatusConstant;
import com.sky.context.BaseContext;
import com.sky.dto.CategoryDTO;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.entity.Category;
import com.sky.exception.DeletionNotAllowedException;
import com.sky.mapper.CategoryMapper;
import com.sky.mapper.DishMapper;
import com.sky.mapper.SetmealMapper;
import com.sky.result.PageResult;
import com.sky.service.CategoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 分类服务实现类
 * 实现分类管理相关的业务逻辑
 * 包括菜品分类和套餐分类的增删改查和状态管理等功能
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-01-01
 */
@Service
@Slf4j
public class CategoryServiceImpl implements CategoryService {

    @Autowired
    private CategoryMapper categoryMapper;

    @Autowired
    private DishMapper dishMapper;

    @Autowired
    private SetmealMapper setmealMapper;

    /**
     * 新增分类
     * 添加新的菜品分类或套餐分类到系统中
     * 新增的分类默认状态为禁用，需要手动启用
     * 自动设置创建时间、更新时间、创建人、更新人等公共字段
     *
     * @param categoryDTO 分类数据传输对象，包含分类名称、类型、排序等信息
     * @throws RuntimeException 当分类名称重复或数据验证失败时抛出异常
     */
    @Override
    public void save(CategoryDTO categoryDTO) {
        log.info("新增分类：{}", categoryDTO);

        Category category = new Category();
        BeanUtils.copyProperties(categoryDTO, category);

        // 设置默认状态为禁用
        category.setStatus(StatusConstant.DISABLE);

        // 设置创建时间和更新时间
        category.setCreateTime(LocalDateTime.now());
        category.setUpdateTime(LocalDateTime.now());

        // 设置创建人和更新人
        category.setCreateUser(BaseContext.getCurrentId());
        category.setUpdateUser(BaseContext.getCurrentId());

        categoryMapper.insert(category);
        log.info("分类新增成功，ID：{}", category.getId());
    }
    /**
     * 分类分页查询
     * 根据查询条件进行分类的分页查询
     * 支持按分类名称、类型等条件筛选，使用PageHelper插件实现分页功能
     *
     * @param categoryPageQueryDTO 分类分页查询条件对象，包含页码、页面大小、分类名称、类型等查询条件
     * @return PageResult 分页查询结果，包含总记录数和当前页数据列表
     * @throws IllegalArgumentException 当分页参数无效时抛出异常
     */
    @Override
    public PageResult pageQuery(CategoryPageQueryDTO categoryPageQueryDTO) {
        log.info("分类分页查询：{}", categoryPageQueryDTO);

        // 设置分页参数
        PageHelper.startPage(categoryPageQueryDTO.getPage(), categoryPageQueryDTO.getPageSize());

        // 执行分页查询，下一条SQL会自动加入limit关键字分页
        Page<Category> page = categoryMapper.pageQuery(categoryPageQueryDTO);

        log.info("分页查询结果：总记录数={}, 当前页记录数={}", page.getTotal(), page.getResult().size());
        return new PageResult(page.getTotal(), page.getResult());
    }

    /**
     * 根据ID删除分类
     * 删除指定的分类，删除前会检查分类是否被菜品或套餐关联
     * 如果有关联则不允许删除，需要先解除关联关系
     *
     * @param id 分类ID，要删除的分类的唯一标识
     * @throws DeletionNotAllowedException 当分类存在关联数据时抛出此异常，不允许删除
     */
    @Override
    public void deleteById(Long id) {
        log.info("删除分类，ID：{}", id);

        // 查询当前分类是否关联了菜品，如果关联了就抛出业务异常
        Integer dishCount = dishMapper.countByCategoryId(id);
        if (dishCount > 0) {
            log.warn("分类ID={}下有{}个菜品，不能删除", id, dishCount);
            throw new DeletionNotAllowedException(MessageConstant.CATEGORY_BE_RELATED_BY_DISH);
        }

        // 查询当前分类是否关联了套餐，如果关联了就抛出业务异常
        Integer setmealCount = setmealMapper.countByCategoryId(id);
        if (setmealCount > 0) {
            log.warn("分类ID={}下有{}个套餐，不能删除", id, setmealCount);
            throw new DeletionNotAllowedException(MessageConstant.CATEGORY_BE_RELATED_BY_SETMEAL);
        }

        // 删除分类数据
        categoryMapper.deleteById(id);
        log.info("分类删除成功，ID：{}", id);
    }

    /**
     * 修改分类
     * 更新分类的基本信息，如分类名称、排序等
     * 自动设置更新时间和更新人信息
     *
     * @param categoryDTO 分类数据传输对象，包含要更新的分类信息
     * @throws RuntimeException 当分类不存在或数据验证失败时抛出异常
     */
    @Override
    public void update(CategoryDTO categoryDTO) {
        log.info("修改分类：{}", categoryDTO);

        Category category = new Category();
        BeanUtils.copyProperties(categoryDTO, category);

        // 设置修改时间、修改人
        category.setUpdateTime(LocalDateTime.now());
        category.setUpdateUser(BaseContext.getCurrentId());

        categoryMapper.update(category);
        log.info("分类修改成功，ID：{}", category.getId());
    }

    /**
     * 启用或禁用分类
     * 修改分类的状态，可以启用或禁用分类
     * 禁用的分类在用户端将不会显示
     * 自动设置更新时间和更新人信息
     *
     * @param status 分类状态，1表示启用，0表示禁用
     * @param id 分类ID，要修改状态的分类的唯一标识
     * @throws RuntimeException 当分类不存在时抛出异常
     */
    @Override
    public void startOrStop(Integer status, Long id) {
        log.info("{}分类，ID：{}", status == StatusConstant.ENABLE ? "启用" : "禁用", id);

        Category category = Category.builder()
                .id(id)
                .status(status)
                .updateTime(LocalDateTime.now())
                .updateUser(BaseContext.getCurrentId())
                .build();

        categoryMapper.update(category);
        log.info("分类状态修改成功，ID：{}，状态：{}", id, status);
    }

    /**
     * 根据类型查询分类列表
     * 查询指定类型的所有启用状态的分类
     * 主要用于下拉选择框和分类导航展示
     *
     * @param type 分类类型，1表示菜品分类，2表示套餐分类，null表示查询所有类型
     * @return List<Category> 符合条件的分类列表，按排序字段排序
     */
    @Override
    public List<Category> list(Integer type) {
        log.info("查询分类列表，类型：{}", type);

        List<Category> categoryList = categoryMapper.list(type);

        log.info("查询到{}个分类", categoryList.size());
        return categoryList;
    }
}
