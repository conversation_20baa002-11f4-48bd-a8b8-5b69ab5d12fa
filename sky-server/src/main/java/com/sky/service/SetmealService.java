package com.sky.service;

import com.sky.dto.SetmealDTO;
import com.sky.dto.SetmealPageQueryDTO;
import com.sky.entity.Setmeal;
import com.sky.result.PageResult;
import com.sky.vo.DishItemVO;
import com.sky.vo.SetmealVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 套餐服务接口
 * 定义套餐管理相关的业务操作方法
 * 包括套餐的增删改查、状态管理和套餐菜品关联管理等功能
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-01-01
 */
@Service
public interface SetmealService {

    /**
     * 新增套餐
     * 该方法用于保存套餐信息，包括套餐基本信息和套餐菜品关联信息
     * 使用事务保证数据一致性，确保套餐和菜品关联同时成功或失败
     *
     * @param setmealDTO 套餐数据传输对象，包含套餐基本信息和关联菜品列表
     * @throws RuntimeException 当数据库操作失败时抛出异常
     */
    void save(SetmealDTO setmealDTO);

    /**
     * 套餐分页查询
     * 根据查询条件进行套餐的分页查询，支持按套餐名称、分类、状态等条件筛选
     * 使用PageHelper插件实现分页功能
     *
     * @param setmealPageQueryDTO 套餐分页查询条件对象，包含页码、页面大小、套餐名称、分类ID、状态等查询条件
     * @return PageResult 分页查询结果，包含总记录数和当前页数据列表
     */
    PageResult pageQuery(SetmealPageQueryDTO setmealPageQueryDTO);

    /**
     * 套餐起售停售
     * 修改套餐的销售状态，可以将套餐设置为起售或停售状态
     *
     * @param status 套餐状态，1表示起售，0表示停售
     * @param id 套餐ID，要修改状态的套餐的唯一标识
     */
    void startOrStop(Integer status, Long id);

    /**
     * 批量删除套餐
     * 根据套餐ID列表批量删除套餐，删除前会检查套餐是否处于起售状态
     * 如果套餐正在起售中，则不允许删除，会抛出异常
     * 删除操作包括删除套餐基本信息和套餐菜品关联信息
     *
     * @param ids 要删除的套餐ID列表
     * @throws RuntimeException 当套餐处于起售状态时抛出此异常，不允许删除
     */
    void deleteByIds(List<Long> ids);

    /**
     * 根据ID查询套餐详情
     * 用于套餐信息回显，查询套餐的基本信息和关联的菜品信息
     * 主要用于编辑套餐时的数据回显功能
     *
     * @param id 套餐ID，要查询的套餐的唯一标识
     * @return SetmealVO 套餐视图对象，包含套餐基本信息和关联菜品列表
     */
    SetmealVO queryById(Long id);

    /**
     * 修改套餐
     * 更新套餐信息，包括套餐基本信息和套餐菜品关联信息
     * 采用先删除后插入的方式更新套餐菜品关联，确保数据的准确性
     * 使用事务保证数据一致性
     *
     * @param setmealDTO 套餐数据传输对象，包含要更新的套餐信息和关联菜品列表
     * @throws RuntimeException 当数据库操作失败时抛出异常
     */
    void update(SetmealDTO setmealDTO);

    /**
     * 条件查询套餐列表
     * 根据套餐条件查询套餐列表，主要用于用户端展示套餐信息
     *
     * @param setmeal 套餐查询条件对象，包含分类ID、状态等查询条件
     * @return List<Setmeal> 符合条件的套餐列表
     */
    List<Setmeal> list(Setmeal setmeal);

    /**
     * 根据套餐ID查询包含的菜品列表
     * 用于用户端查看套餐详情时展示套餐包含的具体菜品信息
     *
     * @param id 套餐ID，要查询菜品的套餐的唯一标识
     * @return List<DishItemVO> 套餐包含的菜品列表，包含菜品名称、图片等信息
     */
    List<DishItemVO> getDishItemById(Long id);

}
