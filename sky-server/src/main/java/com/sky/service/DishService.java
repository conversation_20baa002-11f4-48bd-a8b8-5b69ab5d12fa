package com.sky.service;

import com.sky.dto.DishDTO;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.result.PageResult;
import com.sky.vo.DishVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 菜品服务接口
 * 定义菜品管理相关的业务操作方法
 * 包括菜品的增删改查、状态管理和口味管理等功能
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-01-01
 */
@Service
public interface DishService {

    /**
     * 新增菜品
     * 保存菜品基本信息和口味信息到数据库
     * 使用事务保证菜品和口味数据的一致性
     *
     * @param dishDTO 菜品数据传输对象，包含菜品基本信息和口味列表
     * @throws RuntimeException 当数据库操作失败时抛出异常
     */
    void save(DishDTO dishDTO);

    /**
     * 菜品分页查询
     * 根据查询条件进行菜品的分页查询
     * 支持按菜品名称、分类、状态等条件筛选
     *
     * @param dishPageQueryDTO 菜品分页查询条件对象，包含页码、页面大小、菜品名称、分类ID、状态等查询条件
     * @return PageResult 分页查询结果，包含总记录数和当前页菜品数据列表
     */
    PageResult pageQuery(DishPageQueryDTO dishPageQueryDTO);

    /**
     * 批量删除菜品
     * 根据菜品ID列表批量删除菜品，删除前会进行多项业务规则校验
     * 包括菜品存在性、销售状态、套餐关联等检查
     *
     * @param ids 要删除的菜品ID列表
     * @throws RuntimeException 当菜品不存在、正在售卖或关联套餐时抛出异常
     */
    void deleteBatch(List<Long> ids);

    /**
     * 根据ID查询菜品详情
     * 用于菜品信息回显，一次性查询菜品基本信息和口味信息
     * 主要用于编辑菜品时的数据回显功能
     *
     * @param id 菜品ID，要查询的菜品的唯一标识
     * @return DishVO 菜品视图对象，包含菜品基本信息和口味列表
     */
    DishVO queryById(Long id);

    /**
     * 修改菜品
     * 更新菜品基本信息和口味信息
     * 采用先删除后插入的方式更新菜品口味，确保数据的准确性
     *
     * @param dishDTO 菜品数据传输对象，包含要更新的菜品信息和口味列表
     * @throws RuntimeException 当数据库操作失败时抛出异常
     */
    void update(DishDTO dishDTO);

    /**
     * 根据分类ID查询菜品列表
     * 查询指定分类下的所有菜品信息
     * 主要用于管理端菜品列表展示
     *
     * @param categoryId 分类ID，要查询菜品的分类的唯一标识
     * @return List<Dish> 指定分类下的菜品列表
     */
    List<Dish> list(Long categoryId);

    /**
     * 菜品起售停售
     * 修改菜品的销售状态，可以将菜品设置为起售或停售状态
     *
     * @param status 菜品状态，"1"表示起售，"0"表示停售
     * @param id 菜品ID，要修改状态的菜品的唯一标识
     */
    void status(String status, Integer id);

    /**
     * 条件查询菜品和口味
     * 根据菜品条件查询菜品列表，并同时查询每个菜品的口味信息
     * 主要用于用户端展示菜品信息，包含完整的菜品和口味数据
     *
     * @param dish 菜品查询条件对象，包含分类ID、状态等查询条件
     * @return List<DishVO> 符合条件的菜品列表，每个菜品包含口味信息
     */
    List<DishVO> listWithFlavor(Dish dish);
}
