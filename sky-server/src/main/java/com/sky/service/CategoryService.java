package com.sky.service;

import com.sky.dto.CategoryDTO;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.entity.Category;
import com.sky.result.PageResult;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 分类服务接口
 * 定义分类管理相关的业务操作方法
 * 包括菜品分类和套餐分类的增删改查和状态管理等功能
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-01-01
 */
@Service
public interface CategoryService {

    /**
     * 新增分类
     * 添加新的菜品分类或套餐分类到系统中
     * 支持菜品分类和套餐分类两种类型
     *
     * @param categoryDTO 分类数据传输对象，包含分类名称、类型、排序等信息
     * @throws RuntimeException 当分类名称重复或数据验证失败时抛出异常
     */
     void save(CategoryDTO categoryDTO);

    /**
     * 分类分页查询
     * 根据查询条件进行分类的分页查询
     * 支持按分类名称、类型等条件筛选
     *
     * @param categoryPageQueryDTO 分类分页查询条件对象，包含页码、页面大小、分类名称、类型等查询条件
     * @return PageResult 分页查询结果，包含总记录数和当前页数据列表
     * @throws IllegalArgumentException 当分页参数无效时抛出异常
     */
    PageResult pageQuery(CategoryPageQueryDTO categoryPageQueryDTO);

    /**
     * 根据ID删除分类
     * 删除指定的分类，删除前会检查分类是否被菜品或套餐关联
     * 如果有关联则不允许删除，需要先解除关联关系
     *
     * @param id 分类ID，要删除的分类的唯一标识
     * @throws RuntimeException 当分类不存在或存在关联数据时抛出异常
     */
    void deleteById(Long id);

    /**
     * 修改分类
     * 更新分类的基本信息，如分类名称、排序等
     *
     * @param categoryDTO 分类数据传输对象，包含要更新的分类信息
     * @throws RuntimeException 当分类不存在或数据验证失败时抛出异常
     */
    void update(CategoryDTO categoryDTO);

    /**
     * 启用或禁用分类
     * 修改分类的状态，可以启用或禁用分类
     * 禁用的分类在用户端将不会显示
     *
     * @param status 分类状态，1表示启用，0表示禁用
     * @param id 分类ID，要修改状态的分类的唯一标识
     * @throws RuntimeException 当分类不存在时抛出异常
     */
    void startOrStop(Integer status, Long id);

    /**
     * 根据类型查询分类列表
     * 查询指定类型的所有启用状态的分类
     * 主要用于下拉选择框和分类导航展示
     *
     * @param type 分类类型，1表示菜品分类，2表示套餐分类，null表示查询所有类型
     * @return List<Category> 符合条件的分类列表，按排序字段排序
     */
    List<Category> list(Integer type);
}
