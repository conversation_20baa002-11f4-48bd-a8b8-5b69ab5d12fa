package com.sky.controller.user;

import com.sky.constant.StatusConstant;
import com.sky.entity.Setmeal;
import com.sky.result.Result;
import com.sky.service.SetmealService;
import com.sky.vo.DishItemVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;

/**
 * 用户端套餐控制器
 * 处理用户端套餐浏览相关的请求
 * 提供套餐查询和套餐详情展示功能
 */
@RestController("userSetmealController")
@RequestMapping("/user/setmeal")
@Api(tags = "C端-套餐浏览接口")
public class SetmealController {
    @Autowired
    private SetmealService setmealService;

    /**
     * 根据分类ID查询套餐列表
     * 查询指定分类下所有起售状态的套餐
     * 用于用户端套餐列表展示，用户可以浏览和选择套餐
     *
     * @param categoryId 分类ID，要查询套餐的分类的唯一标识
     * @return Result<List<Setmeal>> 符合条件的套餐列表
     */
    @GetMapping("/list")
    @ApiOperation("根据分类id查询套餐")
    @Cacheable(cacheNames ="setmeal",key="#categoryId")
    public Result<List<Setmeal>> list(Long categoryId) {
        Setmeal setmeal = new Setmeal();
        setmeal.setCategoryId(categoryId);
        setmeal.setStatus(StatusConstant.ENABLE);

        List<Setmeal> list = setmealService.list(setmeal);
        return Result.success(list);
    }

    /**
     * 根据套餐ID查询包含的菜品列表
     * 查询指定套餐包含的所有菜品信息
     * 用于用户端查看套餐详情，了解套餐包含哪些菜品
     *
     * @param id 套餐ID，要查询菜品的套餐的唯一标识
     * @return Result<List<DishItemVO>> 套餐包含的菜品列表，包含菜品名称、图片等信息
     */
    @GetMapping("/dish/{id}")
    @ApiOperation("根据套餐id查询包含的菜品列表")
    public Result<List<DishItemVO>> dishList(@PathVariable("id") Long id) {
        List<DishItemVO> list = setmealService.getDishItemById(id);
        return Result.success(list);
    }
}
