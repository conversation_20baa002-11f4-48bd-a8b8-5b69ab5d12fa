package com.sky.controller.user;

import com.sky.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

/**
 * 用户端店铺控制器
 * 处理用户端关于店铺状态的相关请求
 * 主要用于用户查询店铺的营业状态
 */
@RestController("userShopController")
@RequestMapping("/user/shop")
public class ShopController {
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 查询店铺营业状态
     * 从Redis缓存中获取店铺的当前营业状态
     * 用于用户端判断店铺是否正在营业，决定是否可以下单
     *
     * @return Result<Integer> 店铺营业状态，1表示营业中，0表示打烊
     */
    @GetMapping("/status")
    public Result<Integer> querystatus(){
        Integer status = (Integer) redisTemplate.opsForValue().get("status");
        return Result.success(status);
    }
}
