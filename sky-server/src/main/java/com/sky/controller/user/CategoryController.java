package com.sky.controller.user;

import com.sky.entity.Category;
import com.sky.result.Result;
import com.sky.service.CategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * 用户端分类控制器
 * 处理用户端分类查询相关的请求
 * 提供分类列表展示功能，用于用户端导航和分类浏览
 */
@RestController("userCategoryController")
@RequestMapping("/user/category")
@Api(tags = "C端-分类接口")
public class CategoryController {

    @Autowired
    private CategoryService categoryService;

    /**
     * 根据类型查询分类列表
     * 查询指定类型的所有启用状态的分类
     * 用于用户端分类导航，用户可以按分类浏览菜品或套餐
     *
     * @param type 分类类型，1表示菜品分类，2表示套餐分类，null表示查询所有类型
     * @return Result<List<Category>> 符合条件的分类列表，按排序字段排序
     */
    @GetMapping("/list")
    @ApiOperation("查询分类")
    public Result<List<Category>> list(Integer type) {
        List<Category> list = categoryService.list(type);
        return Result.success(list);
    }
}
