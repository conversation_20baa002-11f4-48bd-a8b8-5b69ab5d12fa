package com.sky.controller.admin;

import com.sky.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

/**
 * 管理端店铺控制器
 * 处理管理端关于店铺营业状态的管理请求
 * 管理员可以设置和查询店铺的营业状态
 */
@RestController("adminShopController")
@RequestMapping("/admin/shop")
public class ShopController {
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 设置店铺营业状态
     * 管理员设置店铺的营业状态，状态信息存储在Redis缓存中
     * 用于控制店铺是否接受用户下单
     *
     * @param status 店铺营业状态，1表示营业中，0表示打烊
     * @return Result<String> 设置结果
     */
    @PutMapping("/{status}")
    public Result<String> status(@PathVariable Integer status){
        System.out.println("status"+status);
        redisTemplate.opsForValue().set("status",status);
        return Result.success();
    }

    /**
     * 查询店铺营业状态
     * 从Redis缓存中获取店铺的当前营业状态
     * 用于管理端显示当前店铺的营业状态
     *
     * @return Result<Integer> 店铺营业状态，1表示营业中，0表示打烊
     */
    @GetMapping("/status")
    public Result<Integer> querystatus(){
        Integer status = (Integer) redisTemplate.opsForValue().get("status");
        return Result.success(status);
    }
}
