package com.sky.controller.admin;

import com.sky.dto.CategoryDTO;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.entity.Category;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.CategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 管理端分类控制器
 * 处理分类管理相关的请求，包括菜品分类和套餐分类的管理
 * 提供分类的增删改查和状态管理功能
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-01-01
 */
@RestController("adminCategoryController")
@RequestMapping("/admin/category")
@Api(tags = "分类相关接口")
@Slf4j
public class CategoryController {

    @Autowired
    private CategoryService categoryService;

    /**
     * 新增分类
     * 添加新的菜品分类或套餐分类到系统中
     * 支持菜品分类和套餐分类两种类型
     *
     * @param categoryDTO 分类数据传输对象，包含分类名称、类型、排序等信息
     * @return Result<String> 新增结果
     */
    @PostMapping
    @ApiOperation("新增分类")
    public Result<String> save(@RequestBody CategoryDTO categoryDTO) {
        log.info("新增分类：{}", categoryDTO);
        categoryService.save(categoryDTO);
        return Result.success();
    }

    /**
     * 分类分页查询
     * 根据查询条件进行分类的分页查询
     * 支持按分类名称、类型等条件筛选
     *
     * @param categoryPageQueryDTO 分类分页查询条件对象，包含页码、页面大小、分类名称、类型等查询条件
     * @return Result<PageResult> 分页查询结果
     */
    @GetMapping("/page")
    @ApiOperation("分页查询")
    public Result<PageResult> page(CategoryPageQueryDTO categoryPageQueryDTO) {
        log.info("分类分页查询：{}", categoryPageQueryDTO);
        PageResult pageResult = categoryService.pageQuery(categoryPageQueryDTO);
        return Result.success(pageResult);
    }













    /**
     * 删除分类
     * 根据分类ID删除指定的分类
     * 删除前会检查分类是否被菜品或套餐关联，如果有关联则不允许删除
     *
     * @param id 分类ID，要删除的分类的唯一标识
     * @return Result<String> 删除结果
     */
    @DeleteMapping
    @ApiOperation("删除分类")
    public Result<String> deleteById(Long id){
        log.info("删除分类：{}", id);
        categoryService.deleteById(id);
        return Result.success();
    }

    /**
     * 修改分类
     * 更新分类的基本信息，如分类名称、排序等
     *
     * @param categoryDTO 分类数据传输对象，包含要更新的分类信息
     * @return Result<String> 修改结果
     */
    @PutMapping
    @ApiOperation("修改分类")
    public Result<String> update(@RequestBody CategoryDTO categoryDTO){
        categoryService.update(categoryDTO);
        return Result.success();
    }

    /**
     * 启用或禁用分类
     * 修改分类的状态，可以启用或禁用分类
     * 禁用的分类在用户端将不会显示
     *
     * @param status 分类状态，1表示启用，0表示禁用
     * @param id 分类ID，要修改状态的分类的唯一标识
     * @return Result<String> 操作结果
     */
    @PostMapping("/status/{status}")
    @ApiOperation("启用禁用分类")
    public Result<String> startOrStop(@PathVariable("status") Integer status, Long id){
        categoryService.startOrStop(status,id);
        return Result.success();
    }

    /**
     * 根据类型查询分类列表
     * 查询指定类型的所有启用状态的分类
     * 主要用于下拉选择框等场景
     *
     * @param type 分类类型，1表示菜品分类，2表示套餐分类，null表示查询所有类型
     * @return Result<List<Category>> 符合条件的分类列表
     */
    @GetMapping("/list")
    @ApiOperation("根据类型查询分类")
    public Result<List<Category>> list(Integer type){
        List<Category> list = categoryService.list(type);
        return Result.success(list);
    }
}
