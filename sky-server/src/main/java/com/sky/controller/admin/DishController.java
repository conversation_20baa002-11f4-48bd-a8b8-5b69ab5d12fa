package com.sky.controller.admin;

import com.sky.dto.DishDTO;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.DishService;
import com.sky.vo.DishVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Delete;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/admin/dish")
@Slf4j
public class DishController {


    @Autowired
    private DishService dishService;
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 新增菜品
     * 添加新的菜品信息到系统中，包括菜品基本信息和口味信息
     *
     * @param dishDTO 菜品数据传输对象，包含菜品基本信息和口味列表
     * @return Result 新增结果
     */
    @PostMapping
    public Result savewithflavor(@RequestBody DishDTO dishDTO){
        String key="dish_"+dishDTO.getCategoryId();
        log.info("新增菜品：{}",dishDTO);
        dishService.save(dishDTO);
        redisTemplate.delete("key");
        return Result.success();
    }

    /**
     * 菜品分页查询
     * 根据查询条件进行菜品的分页查询
     * 支持按菜品名称、分类、状态等条件筛选
     *
     * @param dishPageQueryDTO 菜品分页查询条件对象
     * @return Result<PageResult> 分页查询结果
     */
    @GetMapping("/page")
    public Result<PageResult> page(DishPageQueryDTO dishPageQueryDTO){
        PageResult pageResult=dishService.pageQuery(dishPageQueryDTO);
        return Result.success(pageResult);

    }

    /**
     * 批量删除菜品
     * 根据菜品ID列表批量删除菜品
     * 删除前会检查菜品状态和套餐关联情况
     *
     * @param ids 要删除的菜品ID列表
     * @return Result 删除结果
     */
    @DeleteMapping
    public Result delete(@RequestParam List<Long> ids){
        log.info("批量删除菜品：{}", ids);
        Set keys = redisTemplate.keys("dish_*");
        dishService.deleteBatch(ids);
        redisTemplate.delete(keys);
        return Result.success();
    }

    /**
     * 根据ID查询菜品详情
     * 用于菜品信息回显，查询菜品的基本信息和口味信息
     * 主要用于编辑菜品时的数据回显功能
     *
     * @param id 菜品ID，要查询的菜品的唯一标识
     * @return Result<DishVO> 菜品详细信息，包含口味列表
     */
    @GetMapping("/{id}")
    public Result<DishVO> queryById(@PathVariable Long id){
        DishVO dishVO=dishService.queryById(id);
        return Result.success(dishVO);

    }

    /**
     * 修改菜品
     * 更新菜品的基本信息和口味信息
     *
     * @param dishDTO 菜品数据传输对象，包含要更新的菜品信息和口味列表
     * @return Result 修改结果
     */
    @PutMapping
    public Result update(@RequestBody DishDTO dishDTO){
        dishService.update(dishDTO);
        Set keys = redisTemplate.keys("dish_*");
        redisTemplate.delete(keys);

        return Result.success();

    }

    /**
     * 根据分类ID查询菜品列表
     * 查询指定分类下的所有菜品信息
     * 主要用于管理端菜品列表展示
     *
     * @param categoryId 分类ID，要查询菜品的分类的唯一标识
     * @return Result<List<Dish>> 指定分类下的菜品列表
     */
    @GetMapping("/list")
    public Result<List<Dish>> list(Long categoryId){
         List<Dish> list = dishService.list(categoryId);
        return Result.success(list);

    }

    /**
     * 菜品起售停售
     * 修改菜品的销售状态，可以将菜品设置为起售或停售状态
     *
     * @param status 菜品状态，"1"表示起售，"0"表示停售
     * @param id 菜品ID，要修改状态的菜品的唯一标识
     * @return Result 操作结果
     */
    @PostMapping("/status/{status}")
    public  Result startorstop(@PathVariable String status,Integer id){
        dishService.status(status,id);
        return Result.success();

    }




}
