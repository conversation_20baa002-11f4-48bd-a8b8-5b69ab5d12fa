package com.sky.controller.admin;

import com.sky.dto.SetmealDTO;
import com.sky.dto.SetmealPageQueryDTO;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.SetmealService;
import com.sky.vo.SetmealVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/admin/setmeal")
@Slf4j
public class SetmealController {
    @Autowired
    private SetmealService setmealService;

    /**
     * 新增套餐
     * 添加新的套餐信息到系统中，包括套餐基本信息和套餐菜品关联信息
     *
     * @param setmealDTO 套餐数据传输对象，包含套餐基本信息和关联菜品列表
     * @return Result 新增结果
     */
    @PostMapping
    @CacheEvict(cacheNames = "setmeal",key="#setmealDTO.categoryId")
    public Result save(@RequestBody SetmealDTO setmealDTO){
        log.info("新增套餐：{}",setmealDTO);
        setmealService.save(setmealDTO);

        return Result.success();

    }

    /**
     * 套餐分页查询
     * 根据查询条件进行套餐的分页查询
     * 支持按套餐名称、分类、状态等条件筛选
     *
     * @param setmealPageQueryDTO 套餐分页查询条件对象
     * @return Result<PageResult> 分页查询结果
     */
    @GetMapping("/page")
    public Result Page(SetmealPageQueryDTO setmealPageQueryDTO){
         PageResult pageResult=setmealService.pageQuery(setmealPageQueryDTO);
        return Result.success(pageResult);
    }

    /**
     * 套餐起售停售
     * 修改套餐的销售状态，可以将套餐设置为起售或停售状态
     *
     * @param status 套餐状态，1表示起售，0表示停售
     * @param id 套餐ID，要修改状态的套餐的唯一标识
     * @return Result 操作结果
     */
    @PostMapping("/status/{status}")
    @CacheEvict(cacheNames = "setmeal",allEntries = true)
    public Result startOrStop(@PathVariable Integer status, @RequestParam Long id){
        setmealService.startOrStop(status,id);
        return Result.success();
    }

    /**
     * 批量删除套餐
     * 根据套餐ID列表批量删除套餐
     * 删除前会检查套餐是否处于起售状态
     *
     * @param ids 要删除的套餐ID列表
     * @return Result 删除结果
     */
    @DeleteMapping
    @CacheEvict(cacheNames = "setmeal",allEntries = true)
    public  Result delete(@RequestParam List<Long> ids){
        setmealService.deleteByIds(ids);
        return  Result.success();

    }

    /**
     * 根据ID查询套餐详情
     * 用于套餐信息回显，查询套餐的基本信息和关联的菜品信息
     * 主要用于编辑套餐时的数据回显功能
     *
     * @param id 套餐ID，要查询的套餐的唯一标识
     * @return Result<SetmealVO> 套餐详细信息，包含关联菜品列表
     */
    @GetMapping("/{id}")
    public Result<SetmealVO> queryById(@PathVariable Long id){
        SetmealVO setmealVO=setmealService.queryById(id);
        return  Result.success(setmealVO);
    }

    /**
     * 修改套餐
     * 更新套餐的基本信息和套餐菜品关联信息
     *
     * @param setmealDTO 套餐数据传输对象，包含要更新的套餐信息和关联菜品列表
     * @return Result 修改结果
     */
    @PutMapping
    @CacheEvict(cacheNames = "setmeal",allEntries = true)
    public  Result update(@RequestBody SetmealDTO setmealDTO){
        setmealService.update(setmealDTO);
        return Result.success();
    }



}
