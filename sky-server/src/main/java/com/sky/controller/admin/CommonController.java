package com.sky.controller.admin;

import com.sky.result.Result;
import com.sky.utils.AliOssUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.UUID;

/**
 * 管理端公共控制器
 * 处理通用功能相关的请求，如文件上传等
 * 提供系统公共服务功能
 */
@RestController
@RequestMapping("/admin/common")
@Slf4j
public class CommonController {
    @Autowired
    private AliOssUtil aliOssUtil;

    /**
     * 文件上传
     * 将上传的文件存储到阿里云OSS对象存储服务
     * 支持图片、文档等各种类型文件的上传
     * 自动生成唯一的文件名避免文件名冲突
     *
     * @param file 上传的文件对象，包含文件内容和元数据
     * @return Result<String> 上传结果，成功时返回文件的访问URL地址
     */
     @PostMapping("/upload")
    public Result<String> upload( MultipartFile file){
         log.info("文件上传");
         String originfile=file.getOriginalFilename();
         String extension = originfile.substring(originfile.lastIndexOf("."));
         String filename= UUID.randomUUID().toString()+extension;
         try {
              String url=aliOssUtil.upload(file.getBytes(),filename);
             log.info( "文件上传成功{}",filename);
             return Result.success(url);
         } catch (IOException e) {
             log.info("文件上传失败{}",filename);
             return Result.error("文件上传失败");

         }

    }
}
