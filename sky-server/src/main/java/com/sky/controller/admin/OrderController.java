package com.sky.controller.admin;

import com.sky.dto.OrdersConfirmDTO;
import com.sky.dto.OrdersPageQueryDTO;
import com.sky.dto.OrdersRejectionDTO;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.OrderService;
import com.sky.vo.OrderStatisticsVO;
import com.sky.vo.OrderVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 管理端订单管理控制器
 * 处理管理端订单管理相关的请求
 * 提供订单查询、接单、拒单、取消、派送、完成等功能
 */
@RestController("adminOrderController")
@RequestMapping("/admin/order")
@Slf4j
@Api(tags = "管理端-订单管理接口")
public class OrderController {

    @Autowired
    private OrderService orderService;

    /**
     * 订单搜索
     * 根据查询条件进行订单的分页查询
     * 支持按订单号、手机号、状态等条件筛选
     *
     * @param ordersPageQueryDTO 订单分页查询条件对象
     * @return Result<PageResult> 分页查询结果
     */
    @GetMapping("/conditionSearch")
    @ApiOperation("订单搜索")
    public Result<PageResult> conditionSearch(OrdersPageQueryDTO ordersPageQueryDTO) {
        log.info("订单搜索：{}", ordersPageQueryDTO);
        PageResult pageResult = orderService.pageQuery(ordersPageQueryDTO);
        return Result.success(pageResult);
    }

    /**
     * 各个状态的订单数量统计
     * 统计待接单、待派送、派送中的订单数量
     * 用于管理端首页数据展示
     *
     * @return Result<OrderStatisticsVO> 订单统计结果
     */
    @GetMapping("/statistics")
    @ApiOperation("各个状态的订单数量统计")
    public Result<OrderStatisticsVO> statistics() {
        log.info("查询订单统计数据");
        OrderStatisticsVO orderStatisticsVO = orderService.statistics();
        return Result.success(orderStatisticsVO);
    }

    /**
     * 查询订单详情
     * 根据订单ID查询订单的详细信息，包括订单基本信息和订单明细
     * 用于管理端订单详情页面展示
     *
     * @param id 订单ID，要查询的订单的唯一标识
     * @return Result<OrderVO> 订单详细信息
     */
    @GetMapping("/details/{id}")
    @ApiOperation("查询订单详情")
    public Result<OrderVO> details(@PathVariable("id") Long id) {
        log.info("查询订单详情：{}", id);
        OrderVO orderVO = orderService.details(id);
        return Result.success(orderVO);
    }

    /**
     * 接单
     * 商家接受用户的订单，将订单状态从"待接单"改为"已接单"
     * 只有状态为"待接单"的订单才能进行接单操作
     *
     * @param ordersConfirmDTO 接单确认对象，包含订单ID和状态
     * @return Result 接单结果
     */
    @PutMapping("/confirm")
    @ApiOperation("接单")
    public Result confirm(@RequestBody OrdersConfirmDTO ordersConfirmDTO) {
        log.info("接单：{}", ordersConfirmDTO);
        orderService.confirm(ordersConfirmDTO);
        return Result.success();
    }

    /**
     * 拒单
     * 商家拒绝用户的订单，将订单状态改为"已取消"
     * 只有状态为"待接单"的订单才能进行拒单操作
     * 如果用户已支付，需要进行退款处理
     *
     * @param ordersRejectionDTO 拒单对象，包含订单ID和拒单原因
     * @return Result 拒单结果
     * @throws Exception 当退款操作失败时抛出异常
     */
    @PutMapping("/rejection")
    @ApiOperation("拒单")
    public Result rejection(@RequestBody OrdersRejectionDTO ordersRejectionDTO) throws Exception {
        log.info("拒单：{}", ordersRejectionDTO);
        orderService.rejection(ordersRejectionDTO);
        return Result.success();
    }

    /**
     * 取消订单
     * 商家取消订单，将订单状态改为"已取消"
     * 如果用户已支付，需要进行退款处理
     *
     * @param ordersRejectionDTO 取消订单对象，包含订单ID和取消原因
     * @return Result 取消结果
     * @throws Exception 当退款操作失败时抛出异常
     */
    @PutMapping("/cancel")
    @ApiOperation("取消订单")
    public Result cancel(@RequestBody OrdersRejectionDTO ordersRejectionDTO) throws Exception {
        log.info("取消订单：{}", ordersRejectionDTO);
        orderService.cancel(ordersRejectionDTO);
        return Result.success();
    }

    /**
     * 派送订单
     * 将订单状态从"已接单"改为"派送中"
     * 只有状态为"已接单"的订单才能进行派送操作
     *
     * @param id 订单ID，要派送的订单的唯一标识
     * @return Result 派送结果
     */
    @PutMapping("/delivery/{id}")
    @ApiOperation("派送订单")
    public Result delivery(@PathVariable("id") Long id) {
        log.info("派送订单：{}", id);
        orderService.delivery(id);
        return Result.success();
    }

    /**
     * 完成订单
     * 将订单状态从"派送中"改为"已完成"
     * 只有状态为"派送中"的订单才能进行完成操作
     * 同时记录订单的完成时间
     *
     * @param id 订单ID，要完成的订单的唯一标识
     * @return Result 完成结果
     */
    @PutMapping("/complete/{id}")
    @ApiOperation("完成订单")
    public Result complete(@PathVariable("id") Long id) {
        log.info("完成订单：{}", id);
        orderService.complete(id);
        return Result.success();
    }
}
