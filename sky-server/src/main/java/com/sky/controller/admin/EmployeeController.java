package com.sky.controller.admin;

import com.sky.constant.JwtClaimsConstant;
import com.sky.dto.EmployeeDTO;
import com.sky.dto.EmployeeLoginDTO;
import com.sky.dto.EmployeePageQueryDTO;
import com.sky.entity.Employee;
import com.sky.properties.JwtProperties;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.EmployeeService;
import com.sky.utils.JwtUtil;
import com.sky.vo.EmployeeLoginVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 员工管理
 */
@RestController
@RequestMapping("/admin/employee")
@Slf4j
public class EmployeeController {

    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private JwtProperties jwtProperties;

    /**
     * 员工登录
     * 处理管理端员工登录请求，验证用户名和密码
     * 登录成功后生成JWT令牌并返回员工基本信息
     *
     * @param employeeLoginDTO 员工登录数据传输对象，包含用户名和密码
     * @return Result<EmployeeLoginVO> 登录结果，包含员工信息和JWT令牌
     */
    @PostMapping("/login")
    public Result<EmployeeLoginVO> login(@RequestBody EmployeeLoginDTO employeeLoginDTO) {
        log.info("员工登录：{}", employeeLoginDTO);

        Employee employee = employeeService.login(employeeLoginDTO);

        //登录成功后，生成jwt令牌
        Map<String, Object> claims = new HashMap<>();
        claims.put(JwtClaimsConstant.EMP_ID, employee.getId());
        String token = JwtUtil.createJWT(
                jwtProperties.getAdminSecretKey(),
                jwtProperties.getAdminTtl(),
                claims);

        EmployeeLoginVO employeeLoginVO = EmployeeLoginVO.builder()
                .id(employee.getId())
                .userName(employee.getUsername())
                .name(employee.getName())
                .token(token)
                .build();

        return Result.success(employeeLoginVO);
    }

    /**
     * 员工退出登录
     * 处理管理端员工退出登录请求
     * 由于使用JWT令牌，服务端无需处理令牌失效，客户端删除令牌即可
     *
     * @return Result<String> 退出登录结果
     */
    @PostMapping("/logout")
    public Result<String> logout() {
        return Result.success();
    }

    /**
     * 新增员工
     * 添加新的员工信息到系统中
     * 设置默认密码并进行相关字段的自动填充
     *
     * @param employeeDTO 员工数据传输对象，包含员工基本信息
     * @return Result 新增结果
     */
    @PostMapping
    public Result save(@RequestBody EmployeeDTO employeeDTO){
        System.out.println("当前线程id"+Thread.currentThread().getId());
        log.info("新增员工，员工数据：{}",employeeDTO);
        employeeService.save(employeeDTO);
        return Result.success();
    }

    /**
     * 员工分页查询
     * 根据查询条件进行员工信息的分页查询
     * 支持按员工姓名进行模糊查询
     *
     * @param employeePageQueryDTO 员工分页查询条件对象
     * @return Result<PageResult> 分页查询结果
     */
    @GetMapping("/page")
    public Result<PageResult> page(EmployeePageQueryDTO employeePageQueryDTO){
        PageResult pageResult=employeeService.pageQuery(employeePageQueryDTO);
        return  Result.success(pageResult);
    }

    /**
     * 获取所有员工列表
     * 查询系统中所有员工的基本信息，不进行分页
     * 主要用于下拉选择框等场景
     *
     * @return Result<List<Employee>> 所有员工的列表
     */
    @GetMapping("/list")
    @ApiOperation("获取员工列表")
    public Result<List<Employee>> list(){
        log.info("获取员工列表");
        List<Employee> employeeList = employeeService.list();
        return Result.success(employeeList);
    }

    /**
     * 启用或禁用员工账号
     * 修改员工账号的状态，可以启用或禁用员工账号
     *
     * @param status 员工状态，1表示启用，0表示禁用
     * @param id 员工ID，要修改状态的员工的唯一标识
     * @return Result 操作结果
     */
    @PostMapping("/status/{status}")
    @ApiOperation("员工状态操作")
    public  Result startstatus(@PathVariable Integer status, @RequestParam Long id){
        employeeService.startorstop(status,id);
        return Result.success();
    }

    /**
     * 根据ID查询员工信息
     * 用于员工信息回显，查询指定员工的详细信息
     * 主要用于编辑员工信息时的数据回显功能
     *
     * @param id 员工ID，要查询的员工的唯一标识
     * @return Result<Employee> 员工详细信息
     */
    @GetMapping("/{id}")
    @ApiOperation("根据id查询编辑员工信息")
    public Result<Employee> getById(@PathVariable  Long id){
        Employee employee=employeeService.querInfo(id);
        return Result.success(employee);
    }

    /**
     * 编辑员工信息
     * 更新员工的基本信息，如姓名、用户名、手机号、性别、身份证号等
     *
     * @param employeeDTO 员工数据传输对象，包含要更新的员工信息
     * @return Result 编辑结果
     */
    @PutMapping
    @ApiOperation("编辑员工信息")
    public  Result editEmpInfo(@RequestBody EmployeeDTO employeeDTO){
        employeeService.editEmpInfo(employeeDTO);
        return  Result.success();
    }
}
