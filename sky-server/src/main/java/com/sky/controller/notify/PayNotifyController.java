package com.sky.controller.notify;

import com.sky.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 支付回调相关接口（模拟版本）
 */
@RestController
@RequestMapping("/notify")
@Slf4j
public class PayNotifyController {
    
    @Autowired
    private OrderService orderService;

    /**
     * 支付成功回调（模拟版本）
     * 
     * 实际环境中，这个接口会接收微信支付的回调通知
     * 这里提供一个模拟接口，方便测试
     */
    @PostMapping("/paySuccess")
    public String paySuccessNotify(@RequestParam String orderNumber) {
        log.info("模拟支付成功回调，订单号：{}", orderNumber);
        
        try {
            // 业务处理，修改订单状态
            orderService.paySuccess(orderNumber);
            
            log.info("订单支付成功处理完成，订单号：{}", orderNumber);
            return "SUCCESS";
        } catch (Exception e) {
            log.error("处理支付成功回调失败：", e);
            return "FAIL";
        }
    }
    
    /**
     * 模拟支付接口（用于测试）
     * 前端可以调用这个接口来模拟支付成功
     */
    @PostMapping("/mockPay")
    public String mockPay(@RequestParam String orderNumber) {
        log.info("模拟支付，订单号：{}", orderNumber);
        
        // 模拟支付处理时间
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 直接调用支付成功处理
        return paySuccessNotify(orderNumber);
    }
}
