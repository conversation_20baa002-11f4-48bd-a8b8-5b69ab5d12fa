<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.DishFlavorMapper">

    <insert id="insertBatch" >
        INSERT INTO dish_flavor (dish_id, name, value)
        VALUES
        <foreach collection="flavors" item="fd" separator=",">
            (#{fd.dishId}, #{fd.name}, #{fd.value})
        </foreach>
    </insert>

    <!-- 根据菜品id批量删除口味数据 -->
    <delete id="deleteByDishIds">
        DELETE FROM dish_flavor WHERE dish_id IN
        <foreach collection="dishIds" item="dishId" open="(" separator="," close=")">
            #{dishId}
        </foreach>
    </delete>
    <delete id="deleteByDishId">
        delete  from dish_flavor where dish_id = #{dishId}
    </delete>

    <!-- 根据菜品id查询口味数据 -->
    <select id="getByDishId" resultType="com.sky.entity.DishFlavor">
        SELECT * FROM dish_flavor WHERE dish_id = #{dishId}
    </select>

</mapper>
