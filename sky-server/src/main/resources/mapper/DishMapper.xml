<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.DishMapper">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into dish (name, category_id, price, image, description, status, create_time, update_time, create_user, update_user)
        values (#{name}, #{categoryId}, #{price}, #{image}, #{description}, #{status}, #{createTime}, #{updateTime}, #{createUser}, #{updateUser})
    </insert>

    <!--   删除菜单品种-->
    <select id="countByCategoryId" resultType="java.lang.Integer">
        select count(id) from dish where category_id = #{categoryId}
    </select>

<!--    分页查询-->
    <select id="pageQuery" resultType="com.sky.vo.DishVO">
        select d.*,c.name as categoryName  from dish d left join  category c on d.category_id = c.id
        <where>
            <if test="name !=null and name !=''">
                and d.name like concat('%',#{name},'%')
            </if>
            <if test="categoryId != null">
                and d.category_id = #{categoryId}
            </if>
            <if test="status != null">
                and d.status = #{status}
            </if>
        </where>
    </select>


    <!-- 批量删除菜品 -->
    <delete id="deleteBatch">
        DELETE FROM dish WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 定义ResultMap处理一对多关系 -->
    <resultMap id="dishVOMap" type="com.sky.vo.DishVO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="categoryId" column="category_id"/>
        <result property="price" column="price"/>
        <result property="image" column="image"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <result property="updateTime" column="update_time"/>
        <result property="categoryName" column="categoryName"/>
        <!-- 一对多关系：一个菜品对应多个口味 -->
        <collection property="flavors" ofType="com.sky.entity.DishFlavor">
            <id property="id" column="flavor_id"/>
            <result property="dishId" column="dish_id"/>
            <result property="name" column="flavor_name"/>
            <result property="value" column="flavor_value"/>
        </collection>
    </resultMap>

    <!-- 根据id查询菜品详情（包含口味） - 一次查询搞定 -->
    <select id="queryDish" resultMap="dishVOMap">
        SELECT
            d.id,
            d.name,
            d.category_id,
            d.price,
            d.image,
            d.description,
            d.status,
            d.update_time,
            c.name as categoryName,
            f.id as flavor_id,
            f.dish_id,
            f.name as flavor_name,
            f.value as flavor_value
        FROM dish d
        LEFT JOIN category c ON d.category_id = c.id
        LEFT JOIN dish_flavor f ON d.id = f.dish_id
        WHERE d.id = #{id}
    </select>

    <update id="update">
        update dish
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="categoryId != null">
                category_id = #{categoryId},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="image != null">
                image = #{image},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser}
            </if>
        </set>
        where id = #{id}
    </update>

    <!-- 根据菜品条件查询菜品列表 -->
    <select id="listByCondition" parameterType="Dish" resultType="Dish">
        select * from dish
        <where>
            <if test="name != null and name != ''">
                and name like concat('%',#{name},'%')
            </if>
            <if test="categoryId != null">
                and category_id = #{categoryId}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        order by create_time desc
    </select>

</mapper>
