<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.EmployeeMapper">
    <select id="pageQuery" resultType="com.sky.entity.Employee">
         select * from employee
         <where>
             <if test="name !=null and name!= '' " >
                 and name like concat('%',#{name},'%')
             </if>
         </where>
         order by create_time desc
    </select>
    <update id="update" parameterType="com.sky.entity.Employee">
        UPDATE employee
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="username != null and username != ''">
                username = #{username},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="sex != null and sex != ''">
                sex = #{sex},
            </if>
            <if test="idNumber != null and idNumber != ''">
                id_number = #{idNumber},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
        </set>
        WHERE id = #{id}
    </update>
</mapper>
