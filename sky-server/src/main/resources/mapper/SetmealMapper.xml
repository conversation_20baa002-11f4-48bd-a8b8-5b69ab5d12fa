<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.SetmealMapper">

    <!-- 插入套餐数据 -->
    <insert id="insert" parameterType="Setmeal" useGeneratedKeys="true" keyProperty="id">
            insert into setmeal
            (category_id, name, price, status, description, image, create_time, update_time, create_user, update_user)
            values (#{categoryId}, #{name}, #{price}, #{status}, #{description}, #{image}, #{createTime}, #{updateTime},
                    #{createUser}, #{updateUser})
     </insert>



    <select id="getSetmealIdsByDishIds" resultType="java.lang.Long">
        SELECT DISTINCT setmeal_id FROM setmeal_dish WHERE dish_id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="pageQuery" resultType="com.sky.vo.SetmealVO">
        select setmeal.*, category.name as categoryName
        from setmeal
        left join category on setmeal.category_id = category.id
        <where>
            <if test="name != null and name != ''">
                and setmeal.name like concat('%',#{name},'%')
            </if>
            <if test="categoryId != null">
                and setmeal.category_id = #{categoryId}
            </if>
            <if test="status != null">
                and setmeal.status = #{status}
            </if>
        </where>
        order by setmeal.create_time desc
    </select>
<!--     删除套餐菜品表中的菜品数据-->
    <delete id="deletesetmealdish">
        delete from setmeal_dish  where setmeal_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deletesetmeal">
        delete  from setmeal where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

<!--    编辑提交-->
    <update id="updatesetmeal" parameterType="com.sky.entity.Setmeal">
        update setmeal
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="categoryId != null">
                category_id = #{categoryId},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="image != null">
                image = #{image},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser}
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="list" parameterType="Setmeal" resultType="Setmeal">
        select * from setmeal
        <where>
            <if test="name != null">
                and name like concat('%',#{name},'%')
            </if>
            <if test="categoryId != null">
                and category_id = #{categoryId}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>
</mapper>