package com.sky.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;

public class JwtUtil {
    /**
     * 生成JWT令牌
     * 使用HS256算法生成JWT令牌，用于用户身份认证和授权
     * 令牌包含用户信息和过期时间，用于无状态的身份验证
     *
     * @param secretKey JWT签名密钥，用于保证令牌的安全性和完整性
     * @param ttlMillis JWT过期时间（毫秒），超过此时间令牌将失效
     * @param claims    JWT载荷信息，包含用户ID等自定义声明
     * @return String 生成的JWT令牌字符串
     */
    public static String createJWT(String secretKey, long ttlMillis, Map<String, Object> claims) {
        // 指定签名的时候使用的签名算法，也就是header那部分
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;

        // 生成JWT的时间
        long expMillis = System.currentTimeMillis() + ttlMillis;
        Date exp = new Date(expMillis);

        // 设置jwt的body
        JwtBuilder builder = Jwts.builder()
                // 如果有私有声明，一定要先设置这个自己创建的私有的声明，这个是给builder的claim赋值，一旦写在标准的声明赋值之后，就是覆盖了那些标准的声明的
                .setClaims(claims)
                // 设置签名使用的签名算法和签名使用的秘钥
                .signWith(signatureAlgorithm, secretKey.getBytes(StandardCharsets.UTF_8))
                // 设置过期时间
                .setExpiration(exp);

        return builder.compact();
    }

    /**
     * 解析JWT令牌
     * 验证JWT令牌的有效性并解析出其中的载荷信息
     * 用于从请求中提取用户身份信息进行身份验证
     *
     * @param secretKey JWT签名密钥，必须与生成令牌时使用的密钥一致，此密钥必须保密
     * @param token     待解析的JWT令牌字符串
     * @return Claims JWT载荷对象，包含用户信息和令牌元数据
     * @throws io.jsonwebtoken.JwtException 当令牌无效、过期或签名验证失败时抛出异常
     */
    public static Claims parseJWT(String secretKey, String token) {
        // 得到DefaultJwtParser
        Claims claims = Jwts.parser()
                // 设置签名的秘钥
                .setSigningKey(secretKey.getBytes(StandardCharsets.UTF_8))
                // 设置需要解析的jwt
                .parseClaimsJws(token).getBody();
        return claims;
    }

}
