package com.sky.aspect;


import com.sky.annotation.Autofill;
import com.sky.context.BaseContext;
import com.sky.enumeration.OperationType;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;

@Aspect
@Slf4j
@Component
public class AutoFillAspect {
    /**
     * 定义切入点
     * 拦截mapper包下所有类的所有方法，且方法上标注了@Autofill注解
     * 用于自动填充公共字段，如创建时间、更新时间、创建人、更新人等
     */
    @Pointcut("execution(* com.sky.mapper.*.*(..)) && @annotation(com.sky.annotation.Autofill)")
    public void autoFillPointcut(){

    }

    /**
     * 前置通知，在目标方法执行前进行公共字段自动填充
     * 根据操作类型（INSERT/UPDATE）自动设置相应的公共字段值
     * INSERT操作：设置创建时间、创建人、更新时间、更新人
     * UPDATE操作：设置更新时间、更新人
     *
     * @param joinPoint 连接点对象，包含目标方法的信息和参数
     * @throws RuntimeException 当反射调用方法失败时抛出运行时异常
     */
    @Before("autoFillPointcut()")
    public void autoFill(JoinPoint joinPoint){
        log.info("开始进行公共字段自动填充...");
        //获取当前被拦截的方法参数，即实体对象，数据库操作类型
        MethodSignature signature=(MethodSignature) joinPoint.getSignature();//获得方法签名
        Autofill autofill=signature.getMethod().getAnnotation(Autofill.class);//注解对象
        OperationType operationType=autofill.value();//获取数据库操作类型
        Object[] args=joinPoint.getArgs();
        if (args ==null||args.length==0){
            return;
        }
        Object entity=args[0];
        LocalDateTime now=LocalDateTime.now();
        if (operationType==OperationType.INSERT ){
            try {
                Method setCreateTime = entity.getClass().getDeclaredMethod("setCreateTime", LocalDateTime.class);
                Method setUpdateUser = entity.getClass().getDeclaredMethod("setUpdateUser", Long.class);
                Method setUpdateTime = entity.getClass().getDeclaredMethod("setUpdateTime", LocalDateTime.class);
                Method setCreateUser = entity.getClass().getDeclaredMethod("setCreateUser", Long.class);

                // 修正：invoke方法的第一个参数必须是目标对象entity
                setCreateUser.invoke(entity, BaseContext.getCurrentId());
                setUpdateTime.invoke(entity, now);
                setCreateTime.invoke(entity, now);
                setUpdateUser.invoke(entity, BaseContext.getCurrentId());
                log.info("公共自动赋值完成");

            } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
                throw new RuntimeException(e);
            }
        } else if(operationType==OperationType.UPDATE){
            try {
                Method setUpdateUser = entity.getClass().getDeclaredMethod("setUpdateUser", Long.class);
                Method setUpdateTime = entity.getClass().getDeclaredMethod("setUpdateTime", LocalDateTime.class);

                // 修正：invoke方法的第一个参数必须是目标对象entity
                setUpdateTime.invoke(entity, now);
                setUpdateUser.invoke(entity, BaseContext.getCurrentId());
                log.info("公共自动赋值完成");

            } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
                throw new RuntimeException(e);
            }
        }
    }




}
