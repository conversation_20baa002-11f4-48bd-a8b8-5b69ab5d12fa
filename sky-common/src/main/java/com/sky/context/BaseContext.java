package com.sky.context;

/**
 * 基础上下文类
 * 用于在当前线程中存储和获取用户ID信息
 * 基于ThreadLocal实现线程隔离，确保多线程环境下的数据安全
 * 主要用于自动填充创建人和更新人字段
 */
public class  BaseContext {

    public static ThreadLocal<Long> threadLocal = new ThreadLocal<>();

    /**
     * 设置当前线程的用户ID
     * 在用户登录成功后，将用户ID存储到当前线程的ThreadLocal中
     *
     * @param id 当前登录用户的ID
     */
    public static void setCurrentId(Long id) {
        threadLocal.set(id);
    }

    /**
     * 获取当前线程的用户ID
     * 从当前线程的ThreadLocal中获取用户ID
     * 用于自动填充创建人和更新人字段
     *
     * @return Long 当前登录用户的ID，如果未设置则返回null
     */
    public static Long getCurrentId() {
        return threadLocal.get();
    }

    /**
     * 移除当前线程的用户ID
     * 清理当前线程的ThreadLocal数据，防止内存泄漏
     * 通常在请求结束时调用
     */
    public static void removeCurrentId() {
        threadLocal.remove();
    }

}
